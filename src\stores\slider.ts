import { FUNCTION_LIST, type FunctionItem } from "@/constants/constant";
import { ConversationType, FunctionType } from "@/constants/enums";
import ChatService from "@/services/chatService";
import type { ConversationInfo } from "@/services/conversationService";
import { defineStore } from "pinia";
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { useChatStore } from "./chat";
import { useConversationStore } from "./conversation";
import { useMedicalRecordStore } from "./medicalRecord";
import { useVoiceStore } from "./voice";

export const useSliderStore = defineStore("slider", () => {
  const chatStore = useChatStore();
  const medicalRecordStore = useMedicalRecordStore();
  const conversationStore = useConversationStore();
  const voiceStore = useVoiceStore();
  const router = useRouter();
  const curFunction = ref(FUNCTION_LIST[0]);
  const curConversation = ref<ConversationInfo | null>(null);
  const isCollapsed = ref(false);
  const loading = ref(false);

  async function clickFunction(item: FunctionItem) {
    loading.value = true;
    ChatService.abortCurrentRequest();
    chatStore.isAiTyping = false;
    curFunction.value = item;
    curConversation.value = null;
    curFunction.value = item;
    chatStore.uploadReportImages = [];
    chatStore.uploadResultImages = [];
    chatStore.messageList = [];
    voiceStore.recognitionText = "";
    curConversation.value = null;
    medicalRecordStore.medicalRecord = undefined;
    if (item.id === FunctionType.CHAT) {
      await conversationStore.getConversationList(1, ConversationType.DIAGNOSIS);
    } else if (item.id === FunctionType.VOICE) {
      await conversationStore.getConversationList(1, ConversationType.VOICE);
    } else if (item.id === FunctionType.KNOWLEDGE) {
      await conversationStore.getConversationList(1, ConversationType.KNOWLEDGE);
    }
    router.push(`/${item.id}`);
    loading.value = false;
  }

  async function clickHistory(item: ConversationInfo) {
    loading.value = true;
    chatStore.isAiTyping = false;
    curConversation.value = item;
    chatStore.uploadReportImages = [];
    chatStore.uploadResultImages = [];
    if (curFunction.value.id === FunctionType.KNOWLEDGE) {
      await chatStore.getKnowledgeMessageList(item.conversation_id);
    } else {
      await chatStore.getMessageList(item.conversation_id);
    }
    if (item.voice_medical_records_id) {
      const result = await medicalRecordStore.getVoiceMedicalRecord(item.voice_medical_records_id)
      medicalRecordStore.medicalRecord = result
    }
    loading.value = false;
  }

  onMounted(() => {
    const functionItem = FUNCTION_LIST.find(
      (item) => item.id === window.location.pathname.slice(1)
    );
    if (!!functionItem) {
      curFunction.value = functionItem;
    }
  });

  // 创建新对话
  const clickNewChat = () => {
    ChatService.abortCurrentRequest();
    chatStore.isAiTyping = false;
    curConversation.value = null;
    chatStore.uploadReportImages = [];
    chatStore.uploadResultImages = [];
    chatStore.messageList = [];
    medicalRecordStore.medicalRecord = undefined;
  };

  return {
    loading,
    curFunction,
    curConversation,
    isCollapsed,
    clickFunction,
    clickHistory,
    clickNewChat,
  };
});
