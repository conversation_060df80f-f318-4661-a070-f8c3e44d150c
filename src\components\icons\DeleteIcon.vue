<template>
  <span class="icon-wrapper" v-html="iconSvg"></span>
</template>

<script setup lang="ts">
import { ICONS } from '@/constants/icons';

const iconSvg = ICONS.DELETE;
</script>

<style scoped>
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
}

.icon-wrapper :deep(svg) {
  width: 100%;
  height: 100%;
  fill: currentColor;
}
</style>
