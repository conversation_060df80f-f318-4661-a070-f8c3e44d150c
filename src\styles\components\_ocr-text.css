:deep(.ocr-text table){
  width: 100%;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

:deep(.ocr-text th){
  background: #aaa8a8;
  color: white;
  font-weight: bold;
  padding: 12px 16px;
  text-align: center;
}

:deep(.ocr-text td){
  padding-block: 12px;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
  border-right: 1px solid #d7d7d7;
}

:deep(.ocr-text tr:last-child td){
  border-bottom: none;
}

:deep(.ocr-text tr td:last-child){
  border-right: none;
}

:deep(.ocr-text tr:nth-child(even)){
  background-color: rgb(238, 238, 238);
}

:deep(.ocr-text tr:nth-child(odd)){
  background-color: rgb(255, 255, 255);
}

:deep(.ocr-text th:first-child),
:deep(.ocr-text td:first-child){
  border-left: none;
}

:deep(.ocr-text th:last-child),
:deep(.ocr-text td:last-child){
  border-right: none;
}

:deep(.ocr-text code){
  white-space: pre-wrap;
}
