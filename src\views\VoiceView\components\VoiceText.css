.voice-text-container{
    height: 100%;
}

:deep(.voice-text-card-body){
    height: 55dvh;
    display: flex;
    flex-direction: column;
}

:deep(.voice-text-card-footer){
    display: flex;
    justify-content: center;
    gap: 12px;
}

.text-content {
    flex: 1;
    cursor: pointer;
    padding: 12px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
    min-height: 100px;
}

.text-content:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
}

.text-display {
    white-space: pre-wrap;
    word-break: break-all;
    line-height: 1.6;
    color: #303133;
}

.text-placeholder {
    color: #c0c4cc;
    font-style: italic;
}

.text-editor {
    flex: 1;
}

:deep(.text-editor .el-textarea__inner) {
    min-height: 100px !important;
    resize: none;
}