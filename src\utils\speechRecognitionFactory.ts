/**
 * 语音识别工厂类
 * 根据配置返回相应的语音识别实现
 */

import { SPEECH_RECOGNITION_CONFIG, type SpeechRecognitionType } from '@/constants/constant';
import AliyunSpeechRecognition, { 
  type AliyunSpeechConfig,
  type SpeechRecognitionResult,
  type SpeechRecognitionCallbacks
} from './aliyunSpeechRecognition';
import CustomSpeechRecognition, { 
  type CustomSpeechConfig 
} from './customSpeechRecognition';

// 统一的配置接口
export interface UnifiedSpeechConfig {
  // 阿里云配置
  appKey?: string;
  token?: string;
  domain?: string;
  
  // 自定义服务配置
  wsUrl?: string;
}

// 统一的语音识别接口
export interface ISpeechRecognition {
  initialize(config: UnifiedSpeechConfig, callbacks?: SpeechRecognitionCallbacks): Promise<void>;
  connect(): Promise<void>;
  startRecognition(): Promise<void>;
  sendAudioData(audioData: ArrayBuffer): void;
  stopRecognition(): Promise<void>;
  disconnect(): void;
  getConnectionState(): boolean;
  getCurrentTaskId(): string;
}

// 阿里云语音识别适配器
class AliyunSpeechAdapter implements ISpeechRecognition {
  private instance: AliyunSpeechRecognition;

  constructor() {
    this.instance = new AliyunSpeechRecognition();
  }

  async initialize(config: UnifiedSpeechConfig, callbacks?: SpeechRecognitionCallbacks): Promise<void> {
    const aliyunConfig: AliyunSpeechConfig = {
      appKey: config.appKey!,
      token: config.token!,
      domain: config.domain
    };
    return this.instance.initialize(aliyunConfig, callbacks);
  }

  async connect(): Promise<void> {
    return this.instance.connect();
  }

  async startRecognition(): Promise<void> {
    return this.instance.startRecognition();
  }

  sendAudioData(audioData: ArrayBuffer): void {
    return this.instance.sendAudioData(audioData);
  }

  async stopRecognition(): Promise<void> {
    return this.instance.stopRecognition();
  }

  disconnect(): void {
    return this.instance.disconnect();
  }

  getConnectionState(): boolean {
    return this.instance.getConnectionState();
  }

  getCurrentTaskId(): string {
    return this.instance.getCurrentTaskId();
  }
}

// 自定义语音识别适配器
class CustomSpeechAdapter implements ISpeechRecognition {
  private instance: CustomSpeechRecognition;

  constructor() {
    this.instance = new CustomSpeechRecognition();
  }

  async initialize(config: UnifiedSpeechConfig, callbacks?: SpeechRecognitionCallbacks): Promise<void> {
    const customConfig: CustomSpeechConfig = {
      wsUrl: config.wsUrl || SPEECH_RECOGNITION_CONFIG.CUSTOM.WS_URL,
      token: config.token
    };
    return this.instance.initialize(customConfig, callbacks);
  }

  async connect(): Promise<void> {
    return this.instance.connect();
  }

  async startRecognition(): Promise<void> {
    return this.instance.startRecognition();
  }

  sendAudioData(audioData: ArrayBuffer): void {
    return this.instance.sendAudioData(audioData);
  }

  async stopRecognition(): Promise<void> {
    return this.instance.stopRecognition();
  }

  disconnect(): void {
    return this.instance.disconnect();
  }

  getConnectionState(): boolean {
    return this.instance.getConnectionState();
  }

  getCurrentTaskId(): string {
    return this.instance.getCurrentTaskId();
  }
}

/**
 * 语音识别工厂类
 */
export class SpeechRecognitionFactory {
  /**
   * 创建语音识别实例
   * @param type 语音识别类型，如果不指定则使用配置中的默认类型
   * @returns 语音识别实例
   */
  static createSpeechRecognition(type?: SpeechRecognitionType): ISpeechRecognition {
    const recognitionType = type || SPEECH_RECOGNITION_CONFIG.TYPE;
    
    switch (recognitionType) {
      case 'aliyun':
        console.log('[SpeechFactory] 创建阿里云语音识别实例');
        return new AliyunSpeechAdapter();
      
      case 'custom':
        console.log('[SpeechFactory] 创建自定义语音识别实例');
        return new CustomSpeechAdapter();
      
      default:
        console.warn(`[SpeechFactory] 未知的语音识别类型: ${recognitionType}，使用默认的阿里云实现`);
        return new AliyunSpeechAdapter();
    }
  }

  /**
   * 获取当前配置的语音识别类型
   */
  static getCurrentType(): SpeechRecognitionType {
    return SPEECH_RECOGNITION_CONFIG.TYPE;
  }

  /**
   * 检查指定类型是否可用
   */
  static isTypeAvailable(type: SpeechRecognitionType): boolean {
    switch (type) {
      case 'aliyun':
        return true; // 阿里云实现总是可用
      case 'custom':
        return !!SPEECH_RECOGNITION_CONFIG.CUSTOM.WS_URL; // 检查是否配置了WebSocket URL
      default:
        return false;
    }
  }
}

// 导出类型和接口
export type { SpeechRecognitionResult, SpeechRecognitionCallbacks };
export default SpeechRecognitionFactory;
