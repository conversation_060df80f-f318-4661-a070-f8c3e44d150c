// 阿里云语音服务相关
const aliyunSpeech = {
  zh: {
    tokenIncomplete: '服务器返回的Token信息不完整',
    tokenError: '无法获取阿里云语音识别Token: ',
    useCache: '使用缓存的Token',
    useExpiredCache: '使用可能过期的缓存Token',
    preloadComplete: 'Token预加载完成',
    preloadFailed: 'Token预加载失败:',
    cacheCleared: 'Token缓存已清除'
  },
  en: {
    tokenIncomplete: 'Incomplete token information returned from server',
    tokenError: 'Unable to get Alibaba Cloud speech recognition token: ',
    useCache: 'Using cached token',
    useExpiredCache: 'Using possibly expired cached token',
    preloadComplete: 'Token preloading completed',
    preloadFailed: 'Token preloading failed:',
    cacheCleared: 'Token cache cleared'
  },
  ja: {
    tokenIncomplete: 'サーバーから返されたトークン情報が不完全です',
    tokenError: 'Alibaba Cloud音声認識トークンを取得できません: ',
    useCache: 'キャッシュされたトークンを使用',
    useExpiredCache: '期限切れの可能性があるキャッシュトークンを使用',
    preloadComplete: 'トークンの事前読み込み完了',
    preloadFailed: 'トークンの事前読み込みに失敗:',
    cacheCleared: 'トークンキャッシュがクリアされました'
  },
  ko: {
    tokenIncomplete: '서버에서 반환된 토큰 정보가 불완전합니다',
    tokenError: 'Alibaba Cloud 음성 인식 토큰을 가져올 수 없습니다: ',
    useCache: '캐시된 토큰 사용',
    useExpiredCache: '만료되었을 수 있는 캐시된 토큰 사용',
    preloadComplete: '토큰 사전 로딩 완료',
    preloadFailed: '토큰 사전 로딩 실패:',
    cacheCleared: '토큰 캐시가 지워졌습니다'
  },
  th: {
    tokenIncomplete: 'ข้อมูลโทเค็นที่ส่งคืนจากเซิร์ฟเวอร์ไม่สมบูรณ์',
    tokenError: 'ไม่สามารถรับโทเค็นการรู้จำเสียง Alibaba Cloud: ',
    useCache: 'ใช้โทเค็นที่แคช',
    useExpiredCache: 'ใช้โทเค็นที่แคชซึ่งอาจหมดอายุแล้ว',
    preloadComplete: 'การโหลดโทเค็นล่วงหน้าเสร็จสิ้น',
    preloadFailed: 'การโหลดโทเค็นล่วงหน้าล้มเหลว:',
    cacheCleared: 'แคชโทเค็นถูกล้างแล้ว'
  },
  ms: {
    tokenIncomplete: 'Maklumat token yang dikembalikan dari pelayan tidak lengkap',
    tokenError: 'Tidak dapat mendapatkan token pengecaman suara Alibaba Cloud: ',
    useCache: 'Menggunakan token yang dicache',
    useExpiredCache: 'Menggunakan token cache yang mungkin telah tamat tempoh',
    preloadComplete: 'Pramuatan token selesai',
    preloadFailed: 'Pramuatan token gagal:',
    cacheCleared: 'Cache token telah dibersihkan'
  },
  id: {
    tokenIncomplete: 'Informasi token yang dikembalikan dari server tidak lengkap',
    tokenError: 'Tidak dapat mendapatkan token pengenalan suara Alibaba Cloud: ',
    useCache: 'Menggunakan token yang di-cache',
    useExpiredCache: 'Menggunakan token cache yang mungkin telah kedaluwarsa',
    preloadComplete: 'Pramuat token selesai',
    preloadFailed: 'Pramuat token gagal:',
    cacheCleared: 'Cache token telah dibersihkan'
  },
  vi: {
    tokenIncomplete: 'Thông tin token trả về từ server không đầy đủ',
    tokenError: 'Không thể lấy token nhận dạng giọng nói Alibaba Cloud: ',
    useCache: 'Sử dụng token đã cache',
    useExpiredCache: 'Sử dụng token cache có thể đã hết hạn',
    preloadComplete: 'Tải trước token hoàn thành',
    preloadFailed: 'Tải trước token thất bại:',
    cacheCleared: 'Cache token đã được xóa'
  }
};

// 聊天服务相关
const chat = {
  zh: {
    defaultDiagnosisMessage: '根据化验结果或影响报告进行分析',
    connectionFailed: '连接失败，请稍后重试',
    processingError: '处理错误，请稍候重试',
    sseConnectionError: 'SSE连接错误:',
    retryCount: '重试次数:',
    maxRetriesReached: '已达到最大重试次数，停止重试'
  },
  en: {
    defaultDiagnosisMessage: 'Analysis based on test results or imaging reports',
    connectionFailed: 'Connection failed, please try again later',
    processingError: 'Processing error, please try again later',
    sseConnectionError: 'SSE connection error:',
    retryCount: 'Retry count:',
    maxRetriesReached: 'Maximum retry attempts reached, stopping retries'
  },
  ja: {
    defaultDiagnosisMessage: '検査結果や画像レポートに基づく分析',
    connectionFailed: '接続に失敗しました。しばらくしてからもう一度お試しください',
    processingError: '処理エラー、後でもう一度お試しください',
    sseConnectionError: 'SSE接続エラー:',
    retryCount: '再試行回数:',
    maxRetriesReached: '最大再試行回数に達しました。再試行を停止します'
  },
  ko: {
    defaultDiagnosisMessage: '검사 결과 또는 영상 보고서를 기반으로 한 분석',
    connectionFailed: '연결에 실패했습니다. 잠시 후 다시 시도해주세요',
    processingError: '처리 오류, 잠시 후 다시 시도해주세요',
    sseConnectionError: 'SSE 연결 오류:',
    retryCount: '재시도 횟수:',
    maxRetriesReached: '최대 재시도 횟수에 도달했습니다. 재시도를 중단합니다'
  },
  th: {
    defaultDiagnosisMessage: 'การวิเคราะห์ตามผลการตรวจหรือรายงานภาพ',
    connectionFailed: 'การเชื่อมต่อล้มเหลว กรุณาลองใหม่อีกครั้งในภายหลัง',
    processingError: 'ข้อผิดพลาดในการประมวลผล กรุณาลองใหม่ในภายหลัง',
    sseConnectionError: 'ข้อผิดพลาดการเชื่อมต่อ SSE:',
    retryCount: 'จำนวนครั้งที่ลองใหม่:',
    maxRetriesReached: 'ถึงจำนวนครั้งสูงสุดแล้ว หยุดการลองใหม่'
  },
  ms: {
    defaultDiagnosisMessage: 'Analisis berdasarkan keputusan ujian atau laporan imejan',
    connectionFailed: 'Sambungan gagal, sila cuba lagi kemudian',
    processingError: 'Ralat pemprosesan, sila cuba lagi kemudian',
    sseConnectionError: 'Ralat sambungan SSE:',
    retryCount: 'Kiraan cuba semula:',
    maxRetriesReached: 'Bilangan percubaan maksimum dicapai, menghentikan percubaan'
  },
  id: {
    defaultDiagnosisMessage: 'Analisis berdasarkan hasil tes atau laporan pencitraan',
    connectionFailed: 'Koneksi gagal, silakan coba lagi nanti',
    processingError: 'Kesalahan pemrosesan, silakan coba lagi nanti',
    sseConnectionError: 'Kesalahan koneksi SSE:',
    retryCount: 'Jumlah percobaan ulang:',
    maxRetriesReached: 'Jumlah percobaan maksimum tercapai, menghentikan percobaan'
  },
  vi: {
    defaultDiagnosisMessage: 'Phân tích dựa trên kết quả xét nghiệm hoặc báo cáo hình ảnh',
    connectionFailed: 'Kết nối thất bại, vui lòng thử lại sau',
    processingError: 'Lỗi xử lý, vui lòng thử lại sau',
    sseConnectionError: 'Lỗi kết nối SSE:',
    retryCount: 'Số lần thử lại:',
    maxRetriesReached: 'Đã đạt số lần thử tối đa, dừng thử lại'
  }
};

// 知识库服务相关
const knowledge = {
  zh: {
    connectionFailed: '连接失败，请稍后重试',
    processingError: '处理错误，请稍候重试',
    noContent: '暂无内容',
    unknownDocument: '未知文档',
    sseConnectionError: 'SSE连接错误:',
    retryCount: '重试次数:',
    maxRetriesReached: '已达到最大重试次数，停止重试'
  },
  en: {
    connectionFailed: 'Connection failed, please try again later',
    processingError: 'Processing error, please try again later',
    noContent: 'No content available',
    unknownDocument: 'Unknown document',
    sseConnectionError: 'SSE connection error:',
    retryCount: 'Retry count:',
    maxRetriesReached: 'Maximum retry attempts reached, stopping retries'
  },
  ja: {
    connectionFailed: '接続に失敗しました。しばらくしてからもう一度お試しください',
    processingError: '処理エラー、後でもう一度お試しください',
    noContent: 'コンテンツがありません',
    unknownDocument: '不明なドキュメント',
    sseConnectionError: 'SSE接続エラー:',
    retryCount: '再試行回数:',
    maxRetriesReached: '最大再試行回数に達しました。再試行を停止します'
  },
  ko: {
    connectionFailed: '연결에 실패했습니다. 잠시 후 다시 시도해주세요',
    processingError: '처리 오류, 잠시 후 다시 시도해주세요',
    noContent: '사용 가능한 콘텐츠가 없습니다',
    unknownDocument: '알 수 없는 문서',
    sseConnectionError: 'SSE 연결 오류:',
    retryCount: '재시도 횟수:',
    maxRetriesReached: '최대 재시도 횟수에 도달했습니다. 재시도를 중단합니다'
  },
  th: {
    connectionFailed: 'การเชื่อมต่อล้มเหลว กรุณาลองใหม่อีกครั้งในภายหลัง',
    processingError: 'ข้อผิดพลาดในการประมวลผล กรุณาลองใหม่ในภายหลัง',
    noContent: 'ไม่มีเนื้อหา',
    unknownDocument: 'เอกสารที่ไม่รู้จัก',
    sseConnectionError: 'ข้อผิดพลาดการเชื่อมต่อ SSE:',
    retryCount: 'จำนวนครั้งที่ลองใหม่:',
    maxRetriesReached: 'ถึงจำนวนครั้งสูงสุดแล้ว หยุดการลองใหม่'
  },
  ms: {
    connectionFailed: 'Sambungan gagal, sila cuba lagi kemudian',
    processingError: 'Ralat pemprosesan, sila cuba lagi kemudian',
    noContent: 'Tiada kandungan tersedia',
    unknownDocument: 'Dokumen tidak diketahui',
    sseConnectionError: 'Ralat sambungan SSE:',
    retryCount: 'Kiraan cuba semula:',
    maxRetriesReached: 'Bilangan percubaan maksimum dicapai, menghentikan percubaan'
  },
  id: {
    connectionFailed: 'Koneksi gagal, silakan coba lagi nanti',
    processingError: 'Kesalahan pemrosesan, silakan coba lagi nanti',
    noContent: 'Tidak ada konten tersedia',
    unknownDocument: 'Dokumen tidak dikenal',
    sseConnectionError: 'Kesalahan koneksi SSE:',
    retryCount: 'Jumlah percobaan ulang:',
    maxRetriesReached: 'Jumlah percobaan maksimum tercapai, menghentikan percobaan'
  },
  vi: {
    connectionFailed: 'Kết nối thất bại, vui lòng thử lại sau',
    processingError: 'Lỗi xử lý, vui lòng thử lại sau',
    noContent: 'Không có nội dung',
    unknownDocument: 'Tài liệu không xác định',
    sseConnectionError: 'Lỗi kết nối SSE:',
    retryCount: 'Số lần thử lại:',
    maxRetriesReached: 'Đã đạt số lần thử tối đa, dừng thử lại'
  }
};

// OCR服务相关
const ocr = {
  zh: {
    unsupportedFormat: '不支持的图片格式，请上传 JPG、PNG、GIF 或 WebP 格式的图片',
    fileSizeLimit: '图片大小不能超过5MB'
  },
  en: {
    unsupportedFormat: 'Unsupported image format, please upload JPG, PNG, GIF or WebP format images',
    fileSizeLimit: 'Image size cannot exceed 5MB'
  },
  ja: {
    unsupportedFormat: 'サポートされていない画像形式です。JPG、PNG、GIF、またはWebP形式の画像をアップロードしてください',
    fileSizeLimit: '画像サイズは5MBを超えることはできません'
  },
  ko: {
    unsupportedFormat: '지원되지 않는 이미지 형식입니다. JPG, PNG, GIF 또는 WebP 형식의 이미지를 업로드해주세요',
    fileSizeLimit: '이미지 크기는 5MB를 초과할 수 없습니다'
  },
  th: {
    unsupportedFormat: 'รูปแบบภาพที่ไม่รองรับ กรุณาอัปโหลดภาพในรูปแบบ JPG, PNG, GIF หรือ WebP',
    fileSizeLimit: 'ขนาดภาพต้องไม่เกิน 5MB'
  },
  ms: {
    unsupportedFormat: 'Format imej tidak disokong, sila muat naik imej dalam format JPG, PNG, GIF atau WebP',
    fileSizeLimit: 'Saiz imej tidak boleh melebihi 5MB'
  },
  id: {
    unsupportedFormat: 'Format gambar tidak didukung, silakan unggah gambar dalam format JPG, PNG, GIF atau WebP',
    fileSizeLimit: 'Ukuran gambar tidak boleh melebihi 5MB'
  },
  vi: {
    unsupportedFormat: 'Định dạng hình ảnh không được hỗ trợ, vui lòng tải lên hình ảnh định dạng JPG, PNG, GIF hoặc WebP',
    fileSizeLimit: 'Kích thước hình ảnh không được vượt quá 5MB'
  }
};

// 导出
export const services = {
  zh: {
    aliyunSpeech: aliyunSpeech.zh,
    chat: chat.zh,
    knowledge: knowledge.zh,
    ocr: ocr.zh
  },
  en: {
    aliyunSpeech: aliyunSpeech.en,
    chat: chat.en,
    knowledge: knowledge.en,
    ocr: ocr.en
  },
  ja: {
    aliyunSpeech: aliyunSpeech.ja,
    chat: chat.ja,
    knowledge: knowledge.ja,
    ocr: ocr.ja
  },
  ko: {
    aliyunSpeech: aliyunSpeech.ko,
    chat: chat.ko,
    knowledge: knowledge.ko,
    ocr: ocr.ko
  },
  th: {
    aliyunSpeech: aliyunSpeech.th,
    chat: chat.th,
    knowledge: knowledge.th,
    ocr: ocr.th
  },
  ms: {
    aliyunSpeech: aliyunSpeech.ms,
    chat: chat.ms,
    knowledge: knowledge.ms,
    ocr: ocr.ms
  },
  id: {
    aliyunSpeech: aliyunSpeech.id,
    chat: chat.id,
    knowledge: knowledge.id,
    ocr: ocr.id
  },
  vi: {
    aliyunSpeech: aliyunSpeech.vi,
    chat: chat.vi,
    knowledge: knowledge.vi,
    ocr: ocr.vi
  }
};

export default services;