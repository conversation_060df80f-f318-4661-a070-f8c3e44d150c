{"name": "xw-vet-open-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:dev": "vue-tsc -b && vite build --mode development", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@microsoft/fetch-event-source": "^2.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "axios": "^1.11.0", "element-plus": "^2.10.5", "flag-icons": "^7.5.0", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "pinia": "^3.0.3", "vite-plugin-vue-devtools": "^8.0.0", "vue": "^3.5.17", "vue-element-plus-x": "^1.3.2", "vue-i18n": "^11.1.12", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.32.0", "@eslint/json": "^0.13.1", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node": "^24.2.0", "@typescript-eslint/parser": "^8.40.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.4.0", "globals": "^16.3.0", "jiti": "^2.5.1", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.0.4", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^2.2.12"}}