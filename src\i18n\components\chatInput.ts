// ChatInput 组件的国际化内容
export const chatInput = {
  zh: {
    // 通用提示信息
    aiGeneratedDisclaimer: '内容由 AI 生成，请仔细甄别',
    aiReplying: 'AI正在回复中，请稍候...',
    outputInterrupted: '输出已中断，请重新发送消息',
    outputStopped: '输出已中断',
    cannotReplyError: '抱歉，我现在无法回复您的消息，请稍后再试。',
    placeholder: '请输入内容...',
    
    // ChatView 特有内容
    chat: {
      imageTextRecognitionOnly: '仅识别图像中的文字',
      labResults: '化验结果',
      imagingReport: '影像报告',
      enterMessageOrUploadImage: '请输入消息内容或上传图片',
      imageConsultation: '图片问诊'
    },
    
    // KnowledgeView 特有内容
    knowledge: {
      enterDrugOrDisease: '请先输入需要询问的药品或疾病',
      knowledgeQuery: '知识库查询'
    },
    
    // VoiceView 特有内容
    voice: {
      generatePetSuggestions: '根据宠物病历生成宠物建议',
      enterMessageContent: '请输入消息内容'
    }
  },
  
  en: {
    // 通用提示信息
    aiGeneratedDisclaimer: 'Content generated by AI, please verify carefully',
    aiReplying: 'AI is replying, please wait...',
    outputInterrupted: 'Output interrupted, please resend message',
    outputStopped: 'Output stopped',
    cannotReplyError: 'Sorry, I cannot reply to your message right now, please try again later.',
    placeholder: 'Please enter content...',
    
    // ChatView 特有内容
    chat: {
      imageTextRecognitionOnly: 'Text recognition only from images',
      labResults: 'Lab Results',
      imagingReport: 'Imaging Report',
      enterMessageOrUploadImage: 'Please enter message content or upload images',
      imageConsultation: 'Image Consultation'
    },
    
    // KnowledgeView 特有内容
    knowledge: {
      enterDrugOrDisease: 'Please enter the drug or disease you want to inquire about',
      knowledgeQuery: 'Knowledge Query'
    },
    
    // VoiceView 特有内容
    voice: {
      generatePetSuggestions: 'Generate pet suggestions based on medical records',
      enterMessageContent: 'Please enter message content'
    }
  },
  
  ja: {
    // 通用提示信息
    aiGeneratedDisclaimer: 'AIが生成したコンテンツです。慎重にご確認ください',
    aiReplying: 'AIが返信中です。お待ちください...',
    outputInterrupted: '出力が中断されました。メッセージを再送信してください',
    outputStopped: '出力が停止されました',
    cannotReplyError: '申し訳ございませんが、現在メッセージに返信できません。後ほど再試行してください。',
    placeholder: '内容を入力してください...',
    
    // ChatView 特有内容
    chat: {
      imageTextRecognitionOnly: '画像内のテキストのみを認識',
      labResults: '検査結果',
      imagingReport: '画像レポート',
      enterMessageOrUploadImage: 'メッセージ内容を入力するか画像をアップロードしてください',
      imageConsultation: '画像診療'
    },
    
    // KnowledgeView 特有内容
    knowledge: {
      enterDrugOrDisease: 'お問い合わせしたい薬品または疾病を入力してください',
      knowledgeQuery: 'ナレッジ検索'
    },
    
    // VoiceView 特有内容
    voice: {
      generatePetSuggestions: 'ペットのカルテに基づいてペットの提案を生成',
      enterMessageContent: 'メッセージ内容を入力してください'
    }
  },
  
  ko: {
    // 通用提示信息
    aiGeneratedDisclaimer: 'AI가 생성한 콘텐츠입니다. 신중히 확인해 주세요',
    aiReplying: 'AI가 답변 중입니다. 잠시만 기다려 주세요...',
    outputInterrupted: '출력이 중단되었습니다. 메시지를 다시 보내주세요',
    outputStopped: '출력이 중단되었습니다',
    cannotReplyError: '죄송합니다. 지금은 메시지에 답변할 수 없습니다. 나중에 다시 시도해 주세요.',
    placeholder: '내용을 입력해 주세요...',
    
    // ChatView 特有内容
    chat: {
      imageTextRecognitionOnly: '이미지의 텍스트만 인식',
      labResults: '검사 결과',
      imagingReport: '영상 보고서',
      enterMessageOrUploadImage: '메시지 내용을 입력하거나 이미지를 업로드해 주세요',
      imageConsultation: '이미지 진료'
    },
    
    // KnowledgeView 特有内容
    knowledge: {
      enterDrugOrDisease: '문의하실 약품이나 질병을 입력해 주세요',
      knowledgeQuery: '지식 검색'
    },
    
    // VoiceView 特有内容
    voice: {
      generatePetSuggestions: '펫 의료기록을 바탕으로 펫 제안사항 생성',
      enterMessageContent: '메시지 내용을 입력해 주세요'
    }
  },
  
  th: {
    aiGeneratedDisclaimer: 'เนื้อหาที่สร้างโดย AI โปรดตรวจสอบอย่างระมัดระวัง',
    aiReplying: 'AI กำลังตอบกลับ กรุณารอสักครู่...',
    outputInterrupted: 'การส่งออกถูกขัดจังหวะ กรุณาส่งข้อความใหม่',
    outputStopped: 'การส่งออกหยุดแล้ว',
    cannotReplyError: 'ขออภัย ตอนนี้ไม่สามารถตอบข้อความของคุณได้ กรุณาลองใหม่ในภายหลัง',
    placeholder: 'กรุณาใส่เนื้อหาข้อความ...',
    chat: {
      imageTextRecognitionOnly: 'จดจำข้อความในภาพเท่านั้น',
      labResults: 'ผลการตรวจ',
      imagingReport: 'รายงานภาพ',
      enterMessageOrUploadImage: 'กรุณาใส่ข้อความหรืออัปโหลดรูปภาพ',
      imageConsultation: 'การปรึกษาด้วยภาพ'
    },
    knowledge: {
      enterDrugOrDisease: 'กรุณาใส่ยาหรือโรคที่ต้องการสอบถาม',
      knowledgeQuery: 'การค้นหาความรู้'
    },
    voice: {
      generatePetSuggestions: 'สร้างคำแนะนำสัตว์เลี้ยงจากประวัติการรักษา',
      enterMessageContent: 'กรุณาใส่เนื้อหาข้อความ'
    }
  },
  
  ms: {
    aiGeneratedDisclaimer: 'Kandungan dijana oleh AI, sila sahkan dengan teliti',
    aiReplying: 'AI sedang membalas, sila tunggu...',
    outputInterrupted: 'Output terganggu, sila hantar semula mesej',
    outputStopped: 'Output dihentikan',
    cannotReplyError: 'Maaf, saya tidak dapat membalas mesej anda sekarang, sila cuba lagi nanti.',
    placeholder: 'Sila masukkan kandungan...',
    chat: {
      imageTextRecognitionOnly: 'Pengecaman teks daripada imej sahaja',
      labResults: 'Keputusan Makmal',
      imagingReport: 'Laporan Pengimejan',
      enterMessageOrUploadImage: 'Sila masukkan kandungan mesej atau muat naik imej',
      imageConsultation: 'Perundingan Imej'
    },
    knowledge: {
      enterDrugOrDisease: 'Sila masukkan ubat atau penyakit yang ingin ditanya',
      knowledgeQuery: 'Pertanyaan Pengetahuan'
    },
    voice: {
      generatePetSuggestions: 'Jana cadangan haiwan berdasarkan rekod perubatan',
      enterMessageContent: 'Sila masukkan kandungan mesej'
    }
  },
  
  id: {
    aiGeneratedDisclaimer: 'Konten dihasilkan oleh AI, mohon verifikasi dengan hati-hati',
    aiReplying: 'AI sedang membalas, mohon tunggu...',
    outputInterrupted: 'Output terputus, mohon kirim ulang pesan',
    outputStopped: 'Output dihentikan',
    cannotReplyError: 'Maaf, saya tidak dapat membalas pesan Anda saat ini, mohon coba lagi nanti.',
    placeholder: 'Mohon masukkan konten...',
    chat: {
      imageTextRecognitionOnly: 'Pengenalan teks dari gambar saja',
      labResults: 'Hasil Lab',
      imagingReport: 'Laporan Pencitraan',
      enterMessageOrUploadImage: 'Mohon masukkan konten pesan atau unggah gambar',
      imageConsultation: 'Konsultasi Gambar'
    },
    knowledge: {
      enterDrugOrDisease: 'Mohon masukkan obat atau penyakit yang ingin ditanyakan',
      knowledgeQuery: 'Kueri Pengetahuan'
    },
    voice: {
      generatePetSuggestions: 'Buat saran hewan peliharaan berdasarkan catatan medis',
      enterMessageContent: 'Mohon masukkan konten pesan'
    }
  },
  
  vi: {
    aiGeneratedDisclaimer: 'Nội dung được tạo bởi AI, vui lòng xác minh cẩn thận',
    aiReplying: 'AI đang trả lời, vui lòng chờ...',
    outputInterrupted: 'Đầu ra bị gián đoạn, vui lòng gửi lại tin nhắn',
    outputStopped: 'Đầu ra đã dừng',
    cannotReplyError: 'Xin lỗi, tôi không thể trả lời tin nhắn của bạn ngay bây giờ, vui lòng thử lại sau.',
    placeholder: 'Vui lòng nhập nội dung...',
    chat: {
      imageTextRecognitionOnly: 'Chỉ nhận dạng văn bản từ hình ảnh',
      labResults: 'Kết quả Xét nghiệm',
      imagingReport: 'Báo cáo Hình ảnh',
      enterMessageOrUploadImage: 'Vui lòng nhập nội dung tin nhắn hoặc tải lên hình ảnh',
      imageConsultation: 'Tư vấn Hình ảnh'
    },
    knowledge: {
      enterDrugOrDisease: 'Vui lòng nhập thuốc hoặc bệnh bạn muốn hỏi',
      knowledgeQuery: 'Truy vấn Kiến thức'
    },
    voice: {
      generatePetSuggestions: 'Tạo gợi ý thú cưng dựa trên hồ sơ y tế',
      enterMessageContent: 'Vui lòng nhập nội dung tin nhắn'
    }
  }
};