import api from "./api";

/**
 * Cookie 选项接口
 */
export interface CookieOptions {
  /** 过期时间（天数） */
  expires?: number;
  /** 过期时间（Date 对象） */
  expiresDate?: Date;
  /** Cookie 路径 */
  path?: string;
  /** Cookie 域名 */
  domain?: string;
  /** 是否仅 HTTPS */
  secure?: boolean;
  /** 是否仅 HTTP（防止 XSS） */
  httpOnly?: boolean;
  /** SameSite 策略 */
  sameSite?: 'Strict' | 'Lax' | 'None';
}

/**
 * 环境配置
 */
const getEnvironmentConfig = () => {
  const isDevelopment = import.meta.env.MODE === 'development';
  const isProduction = import.meta.env.MODE === 'production';
  // 检测是否为测试环境（IP地址访问）
  const isTestEnvironment = /^\d+\.\d+\.\d+\.\d+$/.test(window.location.hostname);
  // 检测是否为HTTPS
  const isHttps = window.location.protocol === 'https:';
  
  return {
    isDevelopment,
    isProduction,
    isTestEnvironment,
    // 只有在生产环境且是正式域名时才设置domain
    domain: (isProduction && !isTestEnvironment) ? '.xw-vet.com' : "",
    // 只有HTTPS环境下才启用secure
    secure: isHttps,
  };
};

/**
 * Cookie 工具类
 */
export class CookieUtils {
  /**
   * 设置 Cookie
   * @param name Cookie 名称
   * @param value Cookie 值
   * @param options Cookie 选项
   */
  static setCookie(name: string, value: string, options: CookieOptions = {}): void {
    const env = getEnvironmentConfig();
    console.log("domain", env.domain);
    console.log("设置cookie环境信息:", {
      hostname: window.location.hostname,
      protocol: window.location.protocol,
      isTestEnvironment: /^\\d+\\.\\d+\\.\\d+\\.\\d+$/.test(window.location.hostname),
      configDomain: env.domain,
      configSecure: env.secure
    });

    // 合并默认配置和用户配置
    const config: CookieOptions = {
      path: '/',
      domain: env.domain,
      secure: env.secure,
      sameSite: 'Lax',
      ...options,
    };

    let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;

    // 设置过期时间
    if (config.expires) {
      const expiresDate = new Date();
      expiresDate.setTime(expiresDate.getTime() + (config.expires * 24 * 60 * 60 * 1000));
      cookieString += `; expires=${expiresDate.toUTCString()}`;
    } else if (config.expiresDate) {
      cookieString += `; expires=${config.expiresDate.toUTCString()}`;
    }

    // 设置路径
    if (config.path) {
      cookieString += `; path=${config.path}`;
    }

    // 设置域名
    if (config.domain) {
      cookieString += `; domain=${config.domain}`;
    }

    // 设置安全选项
    if (config.secure) {
      cookieString += '; secure';
    }

    if (config.httpOnly) {
      cookieString += '; httpOnly';
    }

    if (config.sameSite) {
      cookieString += `; sameSite=${config.sameSite}`;
    }
    document.cookie = cookieString;
    // 验证是否设置成功
    const setCookieValue = CookieUtils.getCookie(name);
    console.log(setCookieValue ? '登录成功' : '登录失败');
  }

  /**
   * 获取 Cookie 值
   * @param name Cookie 名称
   * @returns Cookie 值，如果不存在则返回 null
   */
  static getCookie(name: string): string | null {
    const nameEQ = encodeURIComponent(name) + '=';
    const cookies = document.cookie.split(';');

    for (let cookie of cookies) {
      cookie = cookie.trim();
      if (cookie.indexOf(nameEQ) === 0) {
        return decodeURIComponent(cookie.substring(nameEQ.length));
      }
    }

    return null;
  }

  /**
   * 删除 Cookie
   * @param name Cookie 名称
   * @param options Cookie 选项（主要用于指定 path 和 domain）
   */
  static removeCookie(name: string, options: Pick<CookieOptions, 'path' | 'domain'> = {}): void {
    const env = getEnvironmentConfig();

    // 设置过期时间为过去的时间来删除 Cookie
    const deleteOptions: CookieOptions = {
      expires: -1,
      path: options.path || '/',
      domain: options.domain || env.domain,
    };

    this.setCookie(name, '', deleteOptions);
  }

  /**
   * 检查 Cookie 是否存在
   * @param name Cookie 名称
   * @returns 是否存在
   */
  static hasCookie(name: string): boolean {
    return this.getCookie(name) !== null;
  }

  /**
   * 获取所有 Cookie
   * @returns Cookie 对象
   */
  static getAllCookies(): Record<string, string> {
    const cookies: Record<string, string> = {};
    const cookieArray = document.cookie.split(';');

    for (let cookie of cookieArray) {
      cookie = cookie.trim();
      const [name, value] = cookie.split('=');
      if (name && value) {
        cookies[decodeURIComponent(name)] = decodeURIComponent(value);
      }
    }

    return cookies;
  }
}

/**
 * Token 相关的专用方法
 */
export class TokenCookieManager {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  // Token 过期时间配置（天数）
  private static readonly SESSION_EXPIRES = undefined; // 会话 Cookie（浏览器关闭时删除）
  private static readonly REMEMBER_EXPIRES = 30; // 记住登录 30 天

  /**
   * 设置登录 Token
   * @param token 访问令牌
   * @param remember 是否记住登录
   * @param refreshToken 刷新令牌（可选）
   */
  static setToken(token: string, remember: boolean = false, refreshToken?: string): void {
    const expires = remember ? this.REMEMBER_EXPIRES : this.SESSION_EXPIRES;
    const env = getEnvironmentConfig();

    // 设置访问令牌
    CookieUtils.setCookie(this.TOKEN_KEY, token, {
      expires,
      secure: env.secure, // 根据环境动态设置
      sameSite: 'Lax',
    });

    // 设置刷新令牌（如果提供）
    if (refreshToken) {
      CookieUtils.setCookie(this.REFRESH_TOKEN_KEY, refreshToken, {
        expires: remember ? this.REMEMBER_EXPIRES * 2 : this.SESSION_EXPIRES, // 刷新令牌有效期更长
        secure: env.secure, // 根据环境动态设置
        sameSite: 'Lax',
      });
    }

    // 同时设置到 API 请求头
    api.setHeader('Authorization', `Bearer ${token}`);
  }

  /**
   * 获取登录 Token
   * @returns Token 值，如果不存在则返回 null
   */
  static getToken(): string | null {
    return CookieUtils.getCookie(this.TOKEN_KEY);
  }

  /**
   * 获取刷新 Token
   * @returns 刷新 Token 值，如果不存在则返回 null
   */
  static getRefreshToken(): string | null {
    return CookieUtils.getCookie(this.REFRESH_TOKEN_KEY);
  }

  /**
   * 删除所有 Token
   */
  static removeToken(): void {
    CookieUtils.removeCookie(this.TOKEN_KEY);
    CookieUtils.removeCookie(this.REFRESH_TOKEN_KEY);

    // 同时从 API 请求头中移除
    api.removeHeader('Authorization');
  }

  /**
   * 检查是否已登录（Token 是否存在）
   * @returns 是否已登录
   */
  static isLoggedIn(): boolean {
    return CookieUtils.hasCookie(this.TOKEN_KEY);
  }

  /**
   * 刷新 Token（如果有刷新令牌的话）
   * @returns 是否成功刷新
   */
  static async refreshTokenIfNeeded(): Promise<boolean> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      return false;
    }

    try {
      // 这里应该调用刷新 Token 的 API
      // const response = await api.post('/auth/refresh', { refresh_token: refreshToken });
      // this.setToken(response.access_token, true, response.refresh_token);
      return true;
    } catch (error) {
      console.error('Token 刷新失败:', error);
      this.removeToken();
      return false;
    }
  }
}

export default CookieUtils;
