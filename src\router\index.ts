import NotFoundView from "@/views/Common/NotFoundView.vue";
import PrivacyPolicy from "@/views/Common/PrivacyPolicy.vue";
import ServiceAgreement from "@/views/Common/ServiceAgreement.vue";
import {
  createRouter,
  createWebHistory,
  type RouteRecordRaw,
} from "vue-router";
import ChatView from "../views/ChatView/index.vue";
import HomeView from "../views/HomeView/HomeView.vue";
import KnowledgeView from "../views/KnowledgeView/index.vue";
import VoiceView from "../views/VoiceView/index.vue";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "home",
    component: HomeView,
  },
  {
    path: "/chat",
    name: "chat",
    component: ChatView,
  },
  {
    path: "/knowledge",
    name: "knowledge",
    component: KnowledgeView,
  },
  {
    path: "/voice",
    name: "voice",
    component: VoiceView,
  },
  {
    path: "/privacy-policy",
    name: "privacyPolicy",
    component: PrivacyPolicy,
  },
  {
    path: "/service-agreement",
    name: "serviceAgreement",
    component: ServiceAgreement,
  },
  {
    path: "/:pathMatch(.*)*",
    name: "notFound",
    component: NotFoundView,
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
