<script lang="ts" setup>
import { LANGUAGE_OPTIONS } from "@/constants/constant";
import { useCommonStore } from "@/stores/common";
import { useSliderStore } from "@/stores/slider";
import { ElDropdown, ElDropdownItem, ElDropdownMenu } from "element-plus";
import { storeToRefs } from "pinia";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";
import GlobeIcon from "../icons/GlobeIcon.vue";

// 全局的Pina库
const sliderStore = useSliderStore();
const commonStore = useCommonStore();
const { locale, t } = useI18n();

// 全局的响应数据
const { curFunction } = storeToRefs(sliderStore);
const { currentLanguageInfo } = storeToRefs(commonStore);

// 组件挂载时设置浏览器默认语言
onMounted(() => {
  commonStore.initializeLanguage();
});
</script>

<template>
  <div class="header-container">
    <div class="header-left"></div>
    <div class="header-center">
      {{ t(curFunction.title) }}
    </div>
    <div class="header-right">
      <el-dropdown class="language-dropdown" trigger="click" @command="commonStore.handleLanguageChange">
        <div class="language-selector">
          <GlobeIcon class="globe-icon"/>
          <span class="language-text">{{ currentLanguageInfo.label }}</span>
          <svg class="dropdown-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10H7Z" fill="currentColor" />
          </svg>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="language-dropdown-menu">
            <el-dropdown-item v-for="option in LANGUAGE_OPTIONS" :key="option.value" :command="option.value"
              :class="{ active: option.value === locale }" class="language-option">
              <span :class="['fi', `fi-${option.flag}`]"></span>
              <span class="label">{{ option.label }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
<style scoped src="./ChatHeader.css"></style>