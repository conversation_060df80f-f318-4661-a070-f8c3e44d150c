<template>
    <svg width="24" height="24" viewBox="0 0 24 24" class="voice-wave-icon" :class="{ 'recording': isRecording }">
        <!-- 5条波形线，代表音频波形 -->
        <rect v-for="(bar, index) in waveBars" :key="index" :x="bar.x" :y="bar.y" :width="bar.width"
            :height="bar.height" rx="1" fill="currentColor" :style="{
                animationDelay: `${index * 0.1}s`,
                transform: `scaleY(${bar.scale})`
            }" class="wave-bar" />
    </svg>
</template>

<script setup lang="ts">
import { computed, onUnmounted, ref, watch } from 'vue';

interface Props {
    isRecording?: boolean;
    audioLevel?: number; // 音频强度 0-1
}

const props = withDefaults(defineProps<Props>(), {
    isRecording: false,
    audioLevel: 0
});

// 波形条的基础配置
const baseWaveBars = [
    { x: 2, width: 2, baseHeight: 8 },
    { x: 6, width: 2, baseHeight: 12 },
    { x: 10, width: 2, baseHeight: 16 },
    { x: 14, width: 2, baseHeight: 12 },
    { x: 18, width: 2, baseHeight: 8 }
];

// 当前波形高度
const waveHeights = ref<number[]>([8, 12, 16, 12, 8]);

// 计算波形条的位置和尺寸
const waveBars = computed(() => {
    return baseWaveBars.map((bar, index) => {
        const height = waveHeights.value[index];
        const y = 12 - height / 2; // 居中对齐
        const scale = props.isRecording ? 1 : 0.3; // 非录音时缩小

        return {
            x: bar.x,
            y,
            width: bar.width,
            height,
            scale
        };
    });
});

// 模拟波形动画
let animationFrame: number | null = null;
let lastTime = 0;

const updateWaveform = (currentTime: number): void => {
    if (currentTime - lastTime > 100) { // 每100ms更新一次
        if (props.isRecording) {
            // 根据音频强度和随机波动更新波形
            const baseLevel = Math.max(0.3, props.audioLevel); // 最小强度0.3

            waveHeights.value = baseWaveBars.map((bar, _index) => {
                // 结合音频强度和随机波动
                const randomFactor = 0.5 + Math.random() * 0.5; // 0.5-1.0
                const audioFactor = baseLevel * (0.8 + Math.random() * 0.4); // 音频影响
                const heightFactor = randomFactor * audioFactor;

                return Math.max(4, Math.min(20, bar.baseHeight * heightFactor));
            });
        } else {
            // 非录音状态，恢复到基础高度
            waveHeights.value = baseWaveBars.map(bar => bar.baseHeight * 0.3);
        }

        lastTime = currentTime;
    }

    if (props.isRecording) {
        animationFrame = requestAnimationFrame(updateWaveform);
    }
};

// 监听录音状态变化
watch(() => props.isRecording, (newValue) => {
    if (newValue) {
        // 开始录音时启动动画
        animationFrame = requestAnimationFrame(updateWaveform);
    } else {
        // 停止录音时停止动画
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
            animationFrame = null;
        }
        // 恢复到静态状态
        waveHeights.value = baseWaveBars.map(bar => bar.baseHeight * 0.3);
    }
});

// 监听音频强度变化（用于更精确的波形控制）
watch(() => props.audioLevel, (newLevel) => {
    if (props.isRecording && newLevel > 0) {
        // 立即响应音频强度变化
        const responsiveFactor = Math.max(0.3, newLevel);
        waveHeights.value = baseWaveBars.map((bar, _index) => {
            const variation = 0.8 + Math.random() * 0.4;
            return Math.max(4, Math.min(20, bar.baseHeight * responsiveFactor * variation));
        });
    }
});

// 组件卸载时清理动画
onUnmounted(() => {
    if (animationFrame) {
        cancelAnimationFrame(animationFrame);
    }
});
</script>

<style scoped>
.voice-wave-icon {
    transition: all 0.3s ease;
}

.wave-bar {
    transition: transform 0.1s ease;
    transform-origin: center bottom;
    animation: wave-pulse 1.5s ease-in-out infinite;
}

.recording .wave-bar {
    animation: wave-recording 0.8s ease-in-out infinite alternate;
}

.recording .wave-bar:nth-child(1) {
    animation-delay: 0s;
}

.recording .wave-bar:nth-child(2) {
    animation-delay: 0.1s;
}

.recording .wave-bar:nth-child(3) {
    animation-delay: 0.2s;
}

.recording .wave-bar:nth-child(4) {
    animation-delay: 0.3s;
}

.recording .wave-bar:nth-child(5) {
    animation-delay: 0.4s;
}

@keyframes wave-pulse {

    0%,
    100% {
        opacity: 0.6;
    }

    50% {
        opacity: 1;
    }
}

@keyframes wave-recording {
    0% {
        transform: scaleY(0.8);
        opacity: 0.7;
    }

    100% {
        transform: scaleY(1.2);
        opacity: 1;
    }
}

/* 悬停效果 */
.voice-wave-icon:hover .wave-bar {
    animation-duration: 0.6s;
}

/* 颜色适配 */
.voice-wave-icon {
    color: inherit;
}
</style>