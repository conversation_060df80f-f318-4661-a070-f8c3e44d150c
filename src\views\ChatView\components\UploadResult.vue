<script setup lang="ts">
import type { ImageAttachment } from "@/services/chatService";
import { OcrService } from "@/services/ocrService";
import {
  extractTablesFromMarkdown,
  parseContentBlocks,
  parseMarkdownTable,
  type TableData,
} from "@/utils/markdownUtils";
import { type UploadRequestOptions } from "element-plus";
import MarkdownIt from "markdown-it";
import { computed, ref, watch, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";
import EditableTable from "./EditableTable.vue";

// 国际化
const { t } = useI18n();

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "complete", files: ImageAttachment[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const ImageAttachments = ref<ImageAttachment[]>([]);
const curFile = ref<ImageAttachment>();
const loadingUids = ref<number[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

const loading = computed((): boolean => {
  return !!(loadingUids.value && loadingUids.value?.length > 0);
});

const successFiles = computed(() => {
  return ImageAttachments.value.filter((file) => file.status === "success");
});

const showFile = computed(() => {
  return curFile.value || successFiles.value[0] || null;
});

const handleUpload = async (options: UploadRequestOptions): Promise<any> => {
  const { file, onSuccess, onError } = options;
  let data: any = {};
  try {
    // 添加到loading列表
    loadingUids.value.push(file.uid);
    const base64 = await OcrService.fileToBase64(file);
    data.base64 = base64;
    const response = await OcrService.ocrText(base64);
    onSuccess(response.result);
    // 保存完整的OCR文本
    data.ocrText = response.result?.text || "";
    // 解析内容块（保持原始顺序）
    data.contentBlocks = parseContentBlocks(response.result?.text || "");
    // 为了兼容性，仍然提取表格内容
    data.tableContent = extractTablesFromMarkdown(response.result?.text || "");
  } catch (err: any) {
    onError(err);
  } finally {
    // 从loading列表中移除
    loadingUids.value = loadingUids.value.filter((uid) => uid !== file.uid);
    return data;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  ImageAttachments.value = [];
  loadingUids.value = [];
};

// 完成上传
const handleComplete = () => {
  emit("complete", successFiles.value);
  handleClose();
};

// 选择文件显示OCR文本
const handleSelectFile = (file: ImageAttachment) => {
  curFile.value = file;
};

// 预览图片（放大显示）
const handlePreviewImage = (file: ImageAttachment) => {
  // 创建预览容器
  const previewContainer = document.createElement("div");
  previewContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    cursor: pointer;
  `;

  const previewImg = document.createElement("img");
  previewImg.src = file.response.base64;
  previewImg.style.cssText = `
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
  `;

  previewContainer.appendChild(previewImg);
  document.body.appendChild(previewContainer);

  // 点击关闭预览
  previewContainer.addEventListener("click", () => {
    document.body.removeChild(previewContainer);
  });
};

// 获取按原始顺序排列的内容块
const contentBlocks = computed(() => {
  return showFile.value?.response?.contentBlocks || [];
});

// 检查是否有内容块
const hasContentBlocks = computed(() => contentBlocks.value.length > 0);

// 渲染文本内容块
const renderTextBlock = (content: string) => {
  const md = new MarkdownIt();
  return md.render(content);
};

// 解析表格内容块
const parseTableBlock = (content: string): TableData[] => {
  return parseMarkdownTable(content);
};

// 用于完整OCR内容的渲染（备用）
const parsedOcrText = computed(() => {
  if (!showFile.value?.response?.ocrText) return null;
  const md = new MarkdownIt();
  return md.render(showFile.value.response.ocrText);
});

// 计算属性：上传状态文本
const uploadLoadingText = computed(() => {
  return t('uploadResult.upload.loading').replace('{count}', loadingUids.value.length.toString());
});

const uploadNormalText = computed(() => {
  return {
    dragText: t('uploadResult.upload.dragText'),
    clickText: t('uploadResult.upload.clickText'),
    pasteText: t('uploadResult.upload.pasteText')
  };
});

// 更新内容块中的表格数据
const handleBlockTableUpdate = (
  updatedMarkdown: string,
  blockIndex: number
) => {
  if (!showFile.value?.response?.contentBlocks) return;

  const blocks = [...showFile.value.response.contentBlocks];
  const targetBlock = blocks[blockIndex];

  if (targetBlock && targetBlock.type === "table") {
    // 更新表格块的内容
    targetBlock.content = updatedMarkdown;

    // 更新当前文件的内容块
    showFile.value.response.contentBlocks = blocks;

    // 重新组合完整的OCR文本
    const combinedContent = blocks.map((block) => block.content).join("\n\n");
    showFile.value.response.ocrText = combinedContent;

    // 同时更新ImageAttachments中对应的文件
    const fileIndex = ImageAttachments.value.findIndex(
      (file) => file.uid === showFile.value?.uid
    );

    if (fileIndex !== -1) {
      ImageAttachments.value[fileIndex].response.contentBlocks = blocks;
      ImageAttachments.value[fileIndex].response.ocrText = combinedContent;
    }
  }
};

// 选择文件显示OCR文本
const handleDeleteFile = (file: ImageAttachment) => {
  // 从ImageAttachments数组中删除指定的文件
  ImageAttachments.value = ImageAttachments.value.filter(
    (item) => item.uid !== file.uid
  );

  // 如果当前选中的文件就是被删除的文件，则清空curFile
  if (curFile.value?.uid === file.uid) {
    curFile.value = undefined;
  }
};

// 处理 Ctrl+V 粘贴上传
const genUid = () => Date.now() + Math.floor(Math.random() * 10000);

const handlePaste = async (e: ClipboardEvent) => {
  if (!e.clipboardData) {
    return;
  }
  const items = e.clipboardData.items;
  const files: File[] = [];
  // 检查 DataTransferItemList
  if (items && items.length > 0) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === "file" && item.type.startsWith("image/")) {
        const file = item.getAsFile();
        if (file) {
          files.push(file);
        }
      }
    }
  }

  // 检查 FileList (备用方案)
  if (files.length === 0 && e.clipboardData.files && e.clipboardData.files.length > 0) {
    for (let i = 0; i < e.clipboardData.files.length; i++) {
      const file = e.clipboardData.files[i];
      if (file.type.startsWith("image/")) {
        files.push(file);
      }
    }
  }
  if (files.length === 0) {
    return;
  }
  e.preventDefault();
  e.stopPropagation();
  for (const file of files) {
    const uid = genUid();
    // 标记loading
    loadingUids.value.push(uid);
    try {
      const base64 = await OcrService.fileToBase64(file);
      const ocr = await OcrService.ocrText(base64);
      const text = ocr.result?.text || "";
      const blocks = parseContentBlocks(text);
      const tableContent = extractTablesFromMarkdown(text);
      const attachment: ImageAttachment = {
        id: uid,
        uid,
        name: file.name || t('uploadResult.upload.pasteFileName').replace('{timestamp}', Date.now().toString()),
        size: file.size,
        response: {
          base64,
          ocrText: text,
          contentBlocks: blocks,
          tableContent,
        },
        raw: file,
        percentage: 100,
        status: "success",
      };
      ImageAttachments.value = [...ImageAttachments.value, attachment];

    } catch (err: any) {
      console.error(t('uploadResult.upload.processingError'), err);
      const attachment: ImageAttachment = {
        id: uid,
        uid,
        name: file.name || t('uploadResult.upload.pasteFileName').replace('{timestamp}', Date.now().toString()),
        size: file.size,
        response: {
          base64: "",
        },
        raw: file,
        percentage: 0,
        status: "error",
        error: err?.message || String(err),
      };
      ImageAttachments.value = [...ImageAttachments.value, attachment];

    } finally {
      loadingUids.value = loadingUids.value.filter((id) => id !== uid);
    }
  }
};

// 仅在弹窗打开时监听粘贴
const pasteListener = (e: Event) => {
  handlePaste(e as ClipboardEvent);
};

watch(dialogVisible, (visible) => {
  if (visible) {
    window.addEventListener("paste", pasteListener);
    // 也在对话框元素上监听，确保能捕获到事件
    document.addEventListener("paste", pasteListener);
  } else {
    window.removeEventListener("paste", pasteListener);
    document.removeEventListener("paste", pasteListener);
  }
});

onBeforeUnmount(() => {
  window.removeEventListener("paste", pasteListener);
  document.removeEventListener("paste", pasteListener);
});

</script>

<template>
  <el-dialog v-model="dialogVisible" :title="t('uploadResult.title')" width="70%" :before-close="handleClose" top="3vh"
    style="margin-bottom: 0" center>
    <div class="upload-result-dialog">
      <div class="upload-section">
        <div class="upload-content" v-if="successFiles && successFiles.length > 0">
          <!-- 按原始顺序展示内容块 -->
          <div v-if="hasContentBlocks">
            <template v-for="(block, index) in contentBlocks" :key="`block-${index}`">
              <!-- 文本内容块 -->
              <div v-if="block.type === 'text'" class="text-block">
                <div v-html="renderTextBlock(block.content)"></div>
              </div>

              <!-- 表格内容块 -->
              <div v-else-if="block.type === 'table'">
                <EditableTable v-for="(table, tableIndex) in parseTableBlock(block.content)"
                  :key="`block-${index}-table-${tableIndex}`" :table-data="table" @update="
                    (markdown: any) => handleBlockTableUpdate(markdown, index)
                  " class="editable-table-wrapper" />
              </div>
            </template>
          </div>

          <!-- 如果没有内容块，显示原始内容（兼容性备用） -->
          <div v-else v-html="parsedOcrText"></div>
        </div>
        <div v-else style="height: 50dvh; text-align: center">
          <img style="height: 100%; border-radius: 5dvw" src="/chat/no_upload_result_files.jpeg" />
        </div>
        <el-upload drag multiple v-model:file-list="ImageAttachments" :http-request="handleUpload" :disabled="loading"
          :show-file-list="false">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            <template v-if="loading">
              {{ uploadLoadingText }}
            </template>
            <template v-else>
              {{ uploadNormalText.dragText }} <em>{{ uploadNormalText.clickText }}</em> 或 <em>{{ uploadNormalText.pasteText }}</em>
            </template>
          </div>
        </el-upload>
      </div>
      <div class="upload-slider" v-if="successFiles && successFiles.length > 0">
        <ul class="upload-file-list">
          <li v-for="file in successFiles" :key="file.id" class="upload-file-item"
            :class="{ active: curFile?.uid === file.uid }" @click="handleSelectFile(file)">
            <div class="upload-file-image-container">
              <el-image :src="file.response.base64" fit="cover" class="upload-file-image" />
              <div class="upload-file-image-options">
                <el-button class="expand-button" size="small" circle @click.stop="handlePreviewImage(file)">
                  <el-icon><zoom-in /></el-icon>
                </el-button>
                <el-button class="delete-button" size="small" circle @click.stop="handleDeleteFile(file)">
                  <el-icon>
                    <delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
            <el-text class="upload-file-title" truncated>
              {{ file.name }}
            </el-text>
          </li>
        </ul>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-button">{{ t('uploadResult.buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleComplete" class="complete-button" :loading="loading">
          {{ t('uploadResult.buttons.complete').replace('{count}', ImageAttachments.filter((f) => f.status === "success").length.toString()) }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped src="./UploadResult.css"></style>
