/**
 * 阿里云实时语音识别工具类
 * 基于阿里云NLS服务实现实时语音转文字功能
 */

import { SPEECH_RECOGNITION_CONFIG } from '@/constants/constant';

export interface AliyunSpeechConfig {
  appKey: string;
  token: string;
  domain?: string;
}

export interface SpeechRecognitionResult {
  text: string;
  isFinal: boolean;
  confidence?: number;
  timestamp?: number;
  isTemp?: boolean;
}

export interface SpeechRecognitionCallbacks {
  onResult?: (result: SpeechRecognitionResult) => void;
  onError?: (error: Error) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onConnect?: () => void;
  onDisconnect?: (code: number, reason: string) => void;
}

class AliyunSpeechRecognition {
  private ws: WebSocket | null = null;
  private config: AliyunSpeechConfig | null = null;
  private callbacks: SpeechRecognitionCallbacks = {};
  private isConnected = false;
  private taskId: string = '';

  /**
   * 初始化语音识别
   * @param config 配置信息
   * @param callbacks 回调函数
   */
  async initialize(config: AliyunSpeechConfig, callbacks: SpeechRecognitionCallbacks = {}) {
    this.config = config;
    this.callbacks = callbacks;
    
    if (!config.token || !config.appKey) {
      throw new Error('阿里云语音识别配置不完整：缺少 token 或 appKey');
    }
  }

  /**
   * 连接WebSocket并发送StartTranscription
   */
  async connect(): Promise<void> {
    if (this.isConnected && this.ws) {
      return;
    }

    return new Promise((resolve, reject) => {
      try {
        const { token } = this.config!;
        // 使用配置中的阿里云语音识别WebSocket地址
        const wsUrl = `${SPEECH_RECOGNITION_CONFIG.ALIYUN.WS_URL}?token=${encodeURIComponent(token)}`;
        
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('[AliyunSR] WebSocket连接已建立');
          this.isConnected = true;
          
          // 连接成功后立即发送StartTranscription消息
          this.taskId = this.generateTaskId();
          const startMessage = {
            header: {
              appkey: this.config!.appKey,
              namespace: SPEECH_RECOGNITION_CONFIG.ALIYUN.NAMESPACE,
              name: SPEECH_RECOGNITION_CONFIG.ALIYUN.MESSAGE_TYPES.START_TRANSCRIPTION,
              task_id: this.taskId,
              message_id: this.generateMessageId()
            },
            payload: SPEECH_RECOGNITION_CONFIG.ALIYUN.AUDIO_FORMAT
          };
          
          this.sendMessage(startMessage);
          console.log('[AliyunSR] 已发送StartTranscription，任务ID:', this.taskId);
          
          this.callbacks.onConnect?.();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          console.log('[AliyunSR] WebSocket连接已关闭:', event.code, event.reason);
          this.isConnected = false;
          this.callbacks.onDisconnect?.(event.code, event.reason);
        };

        this.ws.onerror = (error) => {
          console.error('[AliyunSR] WebSocket错误:', error);
          this.isConnected = false;
          reject(new Error('WebSocket连接失败'));
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 开始语音识别（在connect中已发送StartTranscription）
   */
  async startRecognition(): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket未连接');
    }

    if (!this.taskId) {
      throw new Error('任务ID未生成，请确保WebSocket连接正常');
    }

    this.callbacks.onStart?.();
    console.log('[AliyunSR] 语音识别已准备就绪，任务ID:', this.taskId);
  }

  /**
   * 发送音频数据
   * @param audioData 音频数据buffer
   */
  sendAudioData(audioData: ArrayBuffer): void {
    if (!this.isConnected || !this.ws) {
      console.warn('[AliyunSR] WebSocket未连接，无法发送音频数据');
      return;
    }

    if (this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(audioData);
    }
  }

  /**
   * 停止语音识别
   */
  async stopRecognition(): Promise<void> {
    if (!this.isConnected || !this.ws || !this.taskId) {
      return;
    }

    const stopMessage = {
      header: {
        appkey: this.config!.appKey,
        namespace: SPEECH_RECOGNITION_CONFIG.ALIYUN.NAMESPACE,
        name: SPEECH_RECOGNITION_CONFIG.ALIYUN.MESSAGE_TYPES.STOP_TRANSCRIPTION,
        task_id: this.taskId,
        message_id: this.generateMessageId()
      }
    };

    this.sendMessage(stopMessage);
    this.callbacks.onEnd?.();
    console.log('[AliyunSR] 停止语音识别');
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, '客户端主动断开');
      this.ws = null;
      this.isConnected = false;
      this.taskId = '';
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data);
      console.log('[AliyunSR] 收到消息:', message);

      // 处理连接确认
      if (message.header?.name === SPEECH_RECOGNITION_CONFIG.ALIYUN.MESSAGE_TYPES.TRANSCRIPTION_STARTED) {
        console.log('[AliyunSR] 语音识别已启动');
      }
      // 处理识别结果
      else if (message.header?.name === SPEECH_RECOGNITION_CONFIG.ALIYUN.MESSAGE_TYPES.TRANSCRIPTION_RESULT_CHANGED) {
        const payload = message.payload;
        if (payload && payload.result) {
          const result: SpeechRecognitionResult = {
            text: payload.result,
            isFinal: false,
            confidence: payload.confidence,
            timestamp: Date.now()
          };
          this.callbacks.onResult?.(result);
        }
      }
      // 处理最终结果
      else if (message.header?.name === 'TranscriptionCompleted') {
        const payload = message.payload;
        if (payload && payload.result) {
          const result: SpeechRecognitionResult = {
            text: payload.result,
            isFinal: true,
            confidence: payload.confidence,
            timestamp: Date.now()
          };
          this.callbacks.onResult?.(result);
        }
      }
      // 处理错误
      else if (message.header?.name === 'TaskFailed') {
        const error = new Error(message.payload?.error_message || '语音识别失败');
        this.callbacks.onError?.(error);
      }
    } catch (error) {
      console.warn('[AliyunSR] 解析消息失败:', data);
      console.error(error);
    }
  }

  /**
   * 发送JSON消息
   */
  private sendMessage(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const jsonMessage = JSON.stringify(message);
      this.ws.send(jsonMessage);
      console.log('[AliyunSR] 发送消息:', message);
    } else {
      console.warn('[AliyunSR] WebSocket未开启，无法发送消息');
    }
  }

  /**
   * 生成任务ID (使用UUID格式)
   */
  private generateTaskId(): string {
    return this.generateUUID();
  }

  /**
   * 生成消息ID (使用UUID格式)
   */
  private generateMessageId(): string {
    return this.generateUUID();
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return (String(1e7) + String(-1e3) + String(-4e3) + String(-8e3) + String(-1e11)).replace(/[018]/g, (c: string) =>
      (parseInt(c) ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> parseInt(c) / 4).toString(16)
    ).replace(/-/g, '');
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): boolean {
    return this.isConnected;
  }

  /**
   * 获取当前任务ID
   */
  getCurrentTaskId(): string {
    return this.taskId;
  }
}

export default AliyunSpeechRecognition;