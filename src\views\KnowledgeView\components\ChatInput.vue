<script lang="ts" setup>
import StopIcon from "@/components/icons/StopIcon.vue";
import SubmitArrowIcon from "@/components/icons/SubmitArrowIcon.vue";
import { ConversationType } from '@/constants/enums';
import { type ChatMessage } from '@/services/chatService';
import KnowledgeService, { type BookInfo } from '@/services/knowledgeService';
import { useChatStore } from '@/stores/chat';
import { useConversationStore } from "@/stores/conversation";
import { useSliderStore } from "@/stores/slider";
import { ElMessage } from 'element-plus';
import { storeToRefs } from "pinia";
import { ref } from 'vue';
import { useI18n } from "vue-i18n";

// 国际化
const { t } = useI18n();

// 全局Pina库
const chatStore = useChatStore();
const sliderStore = useSliderStore();
const conversationStore = useConversationStore();

// 全局响应数据
const {
    messageList,
    isAiTyping
} = storeToRefs(chatStore);
const { curConversation } = storeToRefs(sliderStore);

// 全局方法
const { addConversation } = conversationStore;

// 本组件的响应数据
const senderValue = ref("");

// 本组件的方法
function addErrorMessage(msg: string = t('chatInput.outputInterrupted')) {
    const lastAiMessage = messageList.value
        .slice()
        .reverse()
        .find((msg) => msg.type === "ai");
    if (lastAiMessage) {
        lastAiMessage.loading = false;
        lastAiMessage.thinkLoading = false;
        if (!lastAiMessage.content) {
            lastAiMessage.content = msg;
        }
    }
}

// 本组件的方法
const addMessage = (
    messageId: number,
    message: string,
    type: "user" | "ai" = "user",
) => {
    const newMessage: ChatMessage = {
        id: messageId,
        type,
        content: message,
        timestamp: Date.now(),
        loading: type === "ai",
        thinkLoading: type === "ai",
        books: [],
    };
    messageList.value.push(newMessage);
    return newMessage;
};

const updateMessage = (
    messageId: number,
    content: string,
    thinking?: string,
    finished?: boolean,
    thinkFinished?: boolean,
    books?: BookInfo[]
) => {
    const messageIndex = messageList.value.findIndex(
        (msg) => msg.id === messageId
    );
    if (messageIndex !== -1) {
        const message = messageList.value[messageIndex];

        // 批量更新消息属性以减少响应式触发次数
        const updates: Partial<typeof message> = {};

        if (content !== message.content) {
            updates.content = content;
        }
        if (thinking !== undefined && thinking !== message.thinking) {
            updates.thinking = thinking;
        }
        if (finished !== undefined && message.loading !== !finished) {
            updates.loading = !finished;
        }
        if (
            thinkFinished !== undefined &&
            message.thinkLoading !== !thinkFinished
        ) {
            updates.thinkLoading = !thinkFinished;
        }
        if (books && books !== message.books) {
            updates.books = books
        }

        // 只有当有实际更新时才应用更改
        if (Object.keys(updates).length > 0) {
            Object.assign(message, updates);
        }
    }
};

const handleSendMessage = async () => {
    const userMessage = senderValue.value.trim();
    let curConversationId;
    if (!userMessage) {
        ElMessage.warning(t('chatInput.knowledge.enterDrugOrDisease'));
        return;
    }
    if (isAiTyping.value) {
        ElMessage.warning(t('chatInput.aiReplying'));
        return;
    }
    // 清空输入框和附件
    senderValue.value = "";
    if (!!curConversation.value) {
        curConversationId = curConversation.value.conversation_id;
    } else {
        let newConversation;
        newConversation = await addConversation(
            userMessage ? userMessage.slice(0, 20) : t('chatInput.knowledge.knowledgeQuery'),
            ConversationType.KNOWLEDGE
        );
        curConversationId = newConversation.conversation_id;
        curConversation.value = newConversation;
    }
    // 添加用户消息
    addMessage(Date.now() + Math.random(), userMessage, "user");
    isAiTyping.value = true;
    try {
        await KnowledgeService.sendMessageStream(
            {
                message: userMessage,
                conversationId: curConversationId,
            },
            (chunk) => {
                // 更新消息内容
                updateMessage(
                    chunk.messageId,
                    chunk.content,
                    chunk.thinking,
                    chunk.finished,
                    chunk.thinkFinished
                );
            },
            (messageId) => {
                addMessage(messageId, "", "ai");
            },
            (chunk) => {
                // 更新消息内容
                updateMessage(chunk.messageId, "", "", false, false, chunk.books);
            }
        );

    } catch (error) {
        addErrorMessage(t('chatInput.cannotReplyError'));
    } finally {
        isAiTyping.value = false;
    }
}

const handleStopResponse = () => {
    KnowledgeService.abortCurrentRequest();
    isAiTyping.value = false;
    addErrorMessage(t('chatInput.outputStopped'));
};

</script>

<template>
    <div class="chat-input">
        <div class="chat-input-content">
            <MentionSender v-model="senderValue" @submit="handleSendMessage" variant="updown"
                :auto-size="{ minRows: 2, maxRows: 6 }" class="chat-textarea" :placeholder="t('chatInput.placeholder')">
                <template #action-list style="display: flex">
                    <div class="chat-input-main-button">
                        <!-- AI正在回复时显示停止按钮 -->
                        <el-button @mousedown.stop v-if="isAiTyping" circle @click="handleStopResponse()">
                            <el-icon class="stop-icon">
                                <StopIcon />
                            </el-icon>
                        </el-button>
                        <!-- AI未回复时显示发送按钮 -->
                        <el-button @mousedown.stop v-else circle @click="handleSendMessage()">
                            <el-icon class="submit-icon">
                                <SubmitArrowIcon />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </MentionSender>
        </div>
        <div class="chat-input-remarks">{{ t('chatInput.aiGeneratedDisclaimer') }}</div>
    </div>
</template>
<style scoped src="./ChatInput.css"></style>
