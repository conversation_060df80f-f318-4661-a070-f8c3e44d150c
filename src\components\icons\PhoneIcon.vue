<template>
  <span class="icon-wrapper" v-html="iconSvg"></span>
</template>

<script setup lang="ts">
import { ICONS } from '@/constants/icons';

const iconSvg = ICONS.PHONE;
</script>

<style scoped>
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--icon-size, 1em);
  height: var(--icon-size, 1em);
}

.icon-wrapper :deep(svg) {
  width: 100%;
  height: 100%;
  fill: currentColor;
}
</style>
