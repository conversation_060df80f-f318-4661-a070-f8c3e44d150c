import type { Pagination } from "@/services/common";
import {
  ConversationService,
  type ConversationInfo,
} from "@/services/conversationService";
import { ElMessage } from "element-plus";
import { defineStore } from "pinia";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

export const useConversationStore = defineStore("conversation", () => {
  // 状态
  const conversationList = ref<ConversationInfo[]>([]);
  const pagination = ref<Pagination>({
    page: 1,
    size: 15,
  });
  const loading = ref(false);

  // 获取会话列表
  const getConversationList = async (page?: number, conversationType?: number) => {
    try {
      loading.value = true;
      const result = await ConversationService.getConversationListFromApi(page, conversationType);
      conversationList.value = result.items || [];
      pagination.value = result.pagination;
    } finally {
      loading.value = false;
    }
  };

  // 加载更多的会话列表
  const loadMoreConversationList = async (page?: number) => {
    const { t } = useI18n();
    try {
      if (loading.value) {
        ElMessage.warning(t('systemMessages.loadingTooFrequent'));
        return;
      }
      loading.value = true;
      const result = await ConversationService.getConversationListFromApi(page);
      let list = [...conversationList.value, ...result.items];
      if (list.length > 0) {
        const map = new Map();
        list.forEach((item) => {
          if (!map.has(item.id)) {
            map.set(item.id, item);
          }
        });
        list = Array.from(map.values());
      }
      conversationList.value = list;
      pagination.value = result.pagination;
    } finally {
      loading.value = false;
    }
  };

  // 添加新会话
  const addConversation = async (title?: string, conversationType?: number, voiceMedicalRecordsId?: number) => {
    const result = await ConversationService.createConversationFromApi(title, conversationType, voiceMedicalRecordsId);
    conversationList.value.unshift(result);
    return result;
  };

  // 删除会话
  const removeConversation = async (id: number) => {
    await ConversationService.deleteConversationFromApi(id);
  };

  // 重命名会话
  const renameConversation = async (id: number, newTitle: string) => {
    await ConversationService.renameConversationFromApi(id, newTitle);
    const conversation = conversationList.value.find((conv) => conv.id === id);
    if (conversation) {
      conversation.title = newTitle;
    }
  };

  return {
    conversationList,
    loading,
    pagination,
    getConversationList,
    addConversation,
    removeConversation,
    renameConversation,
    loadMoreConversationList,
  };
});
