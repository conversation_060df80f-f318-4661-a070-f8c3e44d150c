.editable-table-container {
  width: 100%;
  overflow-x: auto;
}

.editable-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  table-layout: fixed; /* 等宽布局 */
}

/* 表头行整体渐变背景 */
.editable-table thead tr {
  background: linear-gradient(90deg, #6097fc 0%, #87ceeb 100%);
  position: relative;
}

.table-header {
  color: white;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  border: none; /* 移除单独的边框 */
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 10;
  background: transparent; /* 使用父元素的渐变背景 */
  width: auto; /* 等宽分配 */
}

.table-header:last-child {
  border-right: none;
}

.table-row:nth-child(even) {
  background-color: rgba(135, 206, 235, 0.03);
}

.table-row:hover {
  background-color: rgba(135, 206, 235, 0.08);
}

.table-cell {
  padding: 8px;
  border: 1px solid rgba(135, 206, 235, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  width: auto; /* 等宽分配 */
  vertical-align: middle;
}

.table-cell:hover {
  background-color: rgba(96, 151, 252, 0.05);
  border-color: rgba(96, 151, 252, 0.3);
}

/* 增强编辑状态视觉效果 */
.table-cell.editing {
  background-color: rgba(96, 151, 252, 0.15);
  border-color: #6097fc;
  box-shadow: 0 0 0 3px rgba(96, 151, 252, 0.3), inset 0 0 0 1px #6097fc;
  transform: scale(1.02);
  z-index: 5;
}

.cell-content {
  display: block;
  min-height: 20px;
  word-break: break-word;
}

.cell-input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: inherit;
  font-family: inherit;
  padding: 0;
  margin: 0;
  min-height: 20px;
}

.cell-input:focus {
  outline: none;
}

/* 空单元格样式 */
.table-cell.empty {
  background-color: rgba(255, 193, 7, 0.05);
  border-color: rgba(255, 193, 7, 0.2);
}

.table-cell.empty:hover {
  background-color: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.4);
}

/* 空单元格提示 */
.table-cell.empty:hover .cell-content:empty::before {
  content: "点击编辑";
  color: #999;
  font-style: italic;
  font-size: 12px;
}

/* Element Plus 编辑图标样式 */
.edit-icon {
  position: absolute;
  top: 50%;
  right: 6px;
  transform: translateY(-50%);
  font-size: 12px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  color: rgba(96, 151, 252, 0.8);
}

.table-cell:hover .edit-icon {
  opacity: 1;
}

.table-cell.editing .edit-icon {
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editable-table {
    font-size: 12px;
  }

  .table-header,
  .table-cell {
    padding: 6px 4px;
  }

  .edit-icon {
    font-size: 10px;
    right: 4px;
  }
}
