import { createI18n } from 'vue-i18n';
import { appSlider } from './components/appSlider';
import { bubbleList } from './components/bubbleList';
import { chatInput } from './components/chatInput';
import { dislike } from './components/dislike';
import { feedback } from './components/feedback';
import { loginDialog } from './components/loginDialog';
import { uploadReport } from './components/uploadReport';
import { uploadResult } from './components/uploadResult';
import { voiceInput } from './components/voiceInput';
import { voiceMedicalRecord } from './components/voiceMedicalRecord';
import { voiceText } from './components/voiceText';
import services from './components/services';
import systemMessages from './components/systemMessages';

// 按组件分组的国际化内容结构
const zh = {
    appSlider: { ...appSlider.zh },
    chatInput: { ...chatInput.zh },
    dislike: { ...dislike.zh },
    feedback: { ...feedback.zh },
    loginDialog: { ...loginDialog.zh },
    uploadReport: { ...uploadReport.zh },
    uploadResult: { ...uploadResult.zh },
    voiceInput: { ...voiceInput.zh },
    voiceMedicalRecord: { ...voiceMedicalRecord.zh },
    voiceText: { ...voiceText.zh },
    bubbleList: { ...bubbleList.zh },
    services: { ...services.zh },
    systemMessages: { ...systemMessages.zh },
};

const en = {
    appSlider: { ...appSlider.en },
    chatInput: { ...chatInput.en },
    dislike: { ...dislike.en },
    feedback: { ...feedback.en },
    loginDialog: { ...loginDialog.en },
    uploadReport: { ...uploadReport.en },
    uploadResult: { ...uploadResult.en },
    voiceInput: { ...voiceInput.en },
    voiceMedicalRecord: { ...voiceMedicalRecord.en },
    voiceText: { ...voiceText.en },
    bubbleList: { ...bubbleList.en },
    services: { ...services.en },
    systemMessages: { ...systemMessages.en },
};

const ja = {
    appSlider: { ...appSlider.ja },
    chatInput: { ...chatInput.ja },
    dislike: { ...dislike.ja },
    feedback: { ...feedback.ja },
    loginDialog: { ...loginDialog.ja },
    uploadReport: { ...uploadReport.ja },
    uploadResult: { ...uploadResult.ja },
    voiceInput: { ...voiceInput.ja },
    voiceMedicalRecord: { ...voiceMedicalRecord.ja },
    voiceText: { ...voiceText.ja },
    bubbleList: { ...bubbleList.ja },
    services: { ...services.ja },
    systemMessages: { ...systemMessages.ja },
};

const ko = {
    appSlider: { ...appSlider.ko },
    chatInput: { ...chatInput.ko },
    dislike: { ...dislike.ko },
    feedback: { ...feedback.ko },
    loginDialog: { ...loginDialog.ko },
    uploadReport: { ...uploadReport.ko },
    uploadResult: { ...uploadResult.ko },
    voiceInput: { ...voiceInput.ko },
    voiceMedicalRecord: { ...voiceMedicalRecord.ko },
    voiceText: { ...voiceText.ko },
    bubbleList: { ...bubbleList.ko },
    services: { ...services.ko },
    systemMessages: { ...systemMessages.ko },
};

const th = {
    appSlider: { ...appSlider.th },
    chatInput: { ...chatInput.th },
    dislike: { ...dislike.th },
    feedback: { ...feedback.th },
    loginDialog: { ...loginDialog.th },
    uploadReport: { ...uploadReport.th },
    uploadResult: { ...uploadResult.th },
    voiceInput: { ...voiceInput.th },
    voiceMedicalRecord: { ...voiceMedicalRecord.th },
    voiceText: { ...voiceText.th },
    bubbleList: { ...bubbleList.th },
    services: { ...services.th },
    systemMessages: { ...systemMessages.th },
};

const ms = {
    appSlider: { ...appSlider.ms },
    chatInput: { ...chatInput.ms },
    dislike: { ...dislike.ms },
    feedback: { ...feedback.ms },
    loginDialog: { ...loginDialog.ms },
    uploadReport: { ...uploadReport.ms },
    uploadResult: { ...uploadResult.ms },
    voiceInput: { ...voiceInput.ms },
    voiceMedicalRecord: { ...voiceMedicalRecord.ms },
    voiceText: { ...voiceText.ms },
    bubbleList: { ...bubbleList.ms },
    services: { ...services.ms },
    systemMessages: { ...systemMessages.ms },
};

const id = {
    appSlider: { ...appSlider.id },
    chatInput: { ...chatInput.id },
    dislike: { ...dislike.id },
    feedback: { ...feedback.id },
    loginDialog: { ...loginDialog.id },
    uploadReport: { ...uploadReport.id },
    uploadResult: { ...uploadResult.id },
    voiceInput: { ...voiceInput.id },
    voiceMedicalRecord: { ...voiceMedicalRecord.id },
    voiceText: { ...voiceText.id },
    bubbleList: { ...bubbleList.id },
    services: { ...services.id },
    systemMessages: { ...systemMessages.id },
};

const vi = {
    appSlider: { ...appSlider.vi },
    chatInput: { ...chatInput.vi },
    dislike: { ...dislike.vi },
    feedback: { ...feedback.vi },
    loginDialog: { ...loginDialog.vi },
    uploadReport: { ...uploadReport.vi },
    uploadResult: { ...uploadResult.vi },
    voiceInput: { ...voiceInput.vi },
    voiceMedicalRecord: { ...voiceMedicalRecord.vi },
    voiceText: { ...voiceText.vi },
    bubbleList: { ...bubbleList.vi },
    services: { ...services.vi },
    systemMessages: { ...systemMessages.vi },
};

const messages = {
    zh,
    en,
    ja,
    ko,
    th,
    ms,
    id,
    vi,
};

const i18n = createI18n({
    legacy: false, // 使用 Composition API
    locale: 'zh', // 默认语言
    fallbackLocale: 'zh', // 回退语言
    messages,
});

export default i18n;