/**
 * Markdown 工具函数
 */

/**
 * 表格单元格数据接口
 */
export interface TableCell {
  content: string;
  rowIndex: number;
  colIndex: number;
}

/**
 * 表格行数据接口
 */
export interface TableRow {
  cells: string[];
  isHeader?: boolean;
}

/**
 * 表格数据接口
 */
export interface TableData {
  headers: string[];
  rows: string[][];
  originalMarkdown: string;
}

/**
 * 从 markdown 文本中提取表格
 * @param markdownText markdown 格式的文本
 * @returns 只包含表格的 markdown 文本
 */
export const extractTablesFromMarkdown = (markdownText: string): string => {
  if (!markdownText) return '';

  const lines = markdownText.split('\n');
  const tableLines: string[] = [];
  let inTable = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // 检测表格开始：包含 | 符号的行
    if (line.includes('|') && line.length > 0) {
      if (!inTable) {
        inTable = true;
      }
      tableLines.push(lines[i]); // 保持原始缩进
    } else if (inTable && line === '') {
      // 表格中的空行，继续添加
      tableLines.push(lines[i]);
    } else if (inTable && !line.includes('|')) {
      // 表格结束
      inTable = false;
      // 添加一个空行分隔不同的表格
      if (tableLines.length > 0 && tableLines[tableLines.length - 1].trim() !== '') {
        tableLines.push('');
      }
    }
  }

  return tableLines.join('\n').trim();
};

/**
 * 解析markdown文本，按原始顺序分离表格和非表格内容
 * @param markdownText markdown 格式的文本
 * @returns 包含按顺序排列的内容块数组
 */
export interface ContentBlock {
  type: 'text' | 'table';
  content: string;
  index: number;
}

export const parseContentBlocks = (markdownText: string): ContentBlock[] => {
  if (!markdownText) return [];

  const lines = markdownText.split('\n');
  const blocks: ContentBlock[] = [];
  let currentBlock: string[] = [];
  let currentType: 'text' | 'table' = 'text';
  let inTable = false;
  let blockIndex = 0;

  const finishCurrentBlock = () => {
    if (currentBlock.length > 0) {
      const content = currentBlock.join('\n').trim();
      if (content) {
        blocks.push({
          type: currentType,
          content,
          index: blockIndex++
        });
      }
      currentBlock = [];
    }
  };

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // 检测表格开始：包含 | 符号的行
    if (line.includes('|') && line.length > 0) {
      if (!inTable) {
        // 表格开始，先完成当前文本块
        finishCurrentBlock();
        inTable = true;
        currentType = 'table';
      }
      currentBlock.push(lines[i]);
    } else if (inTable && line === '') {
      // 表格中的空行，继续添加
      currentBlock.push(lines[i]);
    } else if (inTable && !line.includes('|')) {
      // 表格结束
      finishCurrentBlock();
      inTable = false;
      currentType = 'text';
      // 如果这行不是空行，添加到新的文本块中
      if (line.length > 0) {
        currentBlock.push(lines[i]);
      }
    } else if (!inTable) {
      // 非表格内容
      if (currentType !== 'text') {
        finishCurrentBlock();
        currentType = 'text';
      }
      currentBlock.push(lines[i]);
    }
  }

  // 完成最后一个块
  finishCurrentBlock();

  return blocks;
};

/**
 * 解析markdown表格为结构化数据
 * @param markdownTable markdown表格文本
 * @returns 解析后的表格数据
 */
export const parseMarkdownTable = (markdownTable: string): TableData[] => {
  if (!markdownTable) return [];

  const tables: TableData[] = [];
  const lines = markdownTable.split('\n');

  let currentTable: string[] = [];
  let inTable = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    if (trimmedLine.includes('|') && trimmedLine.length > 0) {
      // 检查是否是表格分隔符行（第二行，包含---）
      const isSeparatorLine = trimmedLine.includes('---') || trimmedLine.includes(':--') || trimmedLine.includes('--:');

      if (!inTable) {
        // 开始新表格
        inTable = true;
        currentTable = [line];
      } else {
        currentTable.push(line);
      }

      // 如果是分隔符行，检查下一行是否还是表格行
      if (isSeparatorLine && i + 1 < lines.length) {
        const nextLine = lines[i + 1].trim();
        if (!nextLine.includes('|') || nextLine.length === 0) {
          // 下一行不是表格行，当前表格结束
          const tableData = parseTableLines(currentTable);
          if (tableData) {
            tables.push(tableData);
          }
          currentTable = [];
          inTable = false;
        }
      }
    } else if (inTable) {
      // 空行或非表格行，表格结束
      const tableData = parseTableLines(currentTable);
      if (tableData) {
        tables.push(tableData);
      }
      currentTable = [];
      inTable = false;
    }
  }

  // 处理最后一个表格
  if (currentTable.length > 0) {
    const tableData = parseTableLines(currentTable);
    if (tableData) {
      tables.push(tableData);
    }
  }

  return tables;
};

/**
 * 解析表格行为结构化数据
 * @param lines 表格行数组
 * @returns 表格数据
 */
const parseTableLines = (lines: string[]): TableData | null => {
  if (lines.length < 2) return null;

  const originalMarkdown = lines.join('\n');

  // 解析表头
  const headerLine = lines[0].trim();
  const headers = headerLine.split('|')
    .map(cell => cell.trim().replace(/\*\*/g, '')) // 移除**符号
    .filter(cell => cell !== '');

  // 跳过分隔符行（第二行）
  const dataLines = lines.slice(2);

  // 解析数据行
  const rows: string[][] = [];
  for (const line of dataLines) {
    const trimmedLine = line.trim();
    if (trimmedLine.includes('|')) {
      const cells = trimmedLine.split('|')
        .map(cell => cell.trim())
        .filter((cell, index, arr) => {
          // 过滤掉首尾的空字符串（由于开头和结尾的|导致的）
          return !(cell === '' && (index === 0 || index === arr.length - 1));
        });

      // 确保单元格数量与表头一致
      while (cells.length < headers.length) {
        cells.push('');
      }
      cells.splice(headers.length); // 移除多余的单元格

      rows.push(cells);
    }
  }

  return {
    headers,
    rows,
    originalMarkdown
  };
};

/**
 * 将表格数据重构为markdown格式
 * @param tableData 表格数据
 * @returns markdown格式的表格文本
 */
export const reconstructMarkdownTable = (tableData: TableData): string => {
  if (!tableData.headers.length) return '';

  const lines: string[] = [];

  // 构建表头行
  const headerLine = '| ' + tableData.headers.join(' | ') + ' |';
  lines.push(headerLine);

  // 构建分隔符行
  const separatorLine = '| ' + tableData.headers.map(() => '---').join(' | ') + ' |';
  lines.push(separatorLine);

  // 构建数据行
  for (const row of tableData.rows) {
    const rowLine = '| ' + row.join(' | ') + ' |';
    lines.push(rowLine);
  }

  return lines.join('\n');
};

/**
 * 更新表格中的单元格内容
 * @param tableData 原表格数据
 * @param rowIndex 行索引
 * @param colIndex 列索引
 * @param newContent 新内容
 * @returns 更新后的表格数据
 */
export const updateTableCell = (
  tableData: TableData,
  rowIndex: number,
  colIndex: number,
  newContent: string
): TableData => {
  const updatedTableData = {
    ...tableData,
    rows: tableData.rows.map(row => [...row])
  };

  if (rowIndex >= 0 && rowIndex < updatedTableData.rows.length &&
      colIndex >= 0 && colIndex < updatedTableData.rows[rowIndex].length) {
    updatedTableData.rows[rowIndex][colIndex] = newContent;
  }

  return updatedTableData;
};
