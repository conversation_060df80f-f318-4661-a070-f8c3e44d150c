import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import { createPinia } from "pinia";
import { createApp } from "vue";
import ElementPlusX from "vue-element-plus-x";
import App from "./App.vue";
import "./constants/variables.css";
import i18n from "./i18n";
import router from "./router";
import "/node_modules/flag-icons/css/flag-icons.min.css";

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(ElementPlus);
app.use(ElementPlusX);
app.use(i18n);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.mount("#app");
