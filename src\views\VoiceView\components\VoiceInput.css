.voice-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1dvw;
  margin-top: 1dvw;
}

/* 录音按钮容器 */
.record-button-container {
  position: relative;
}

/* 录音按钮 - 正方形样式 */
.record-button {
  width: 25dvw;
  height: 3.2dvw;
  border: none;
  border-radius: 1dvw;
  background: linear-gradient(145deg, rgb(135, 206, 235) 0%, rgb(96, 151, 252) 50%, rgb(255, 107, 230,0.6) 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
  position: relative;
  overflow: hidden;
}

.record-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.record-button:hover::before {
  width: 300px;
  height: 300px;
  opacity: 0;
}

.record-button:hover {
  box-shadow: 0 4px 20px rgba(74, 144, 226, 0.4), 0 0 0 0 rgba(74, 144, 226, 0.4);
  animation: ripple-hover 1.5s infinite;
}

.record-button:active {
  transform: scale(0.95);
}

/* 涟漪动画 */
@keyframes ripple-hover {
  0% {
    box-shadow: 0 4px 20px rgba(74, 144, 226, 0.4), 0 0 0 0 rgba(74, 144, 226, 0.4);
  }
  70% {
    box-shadow: 0 4px 20px rgba(74, 144, 226, 0.4), 0 0 0 10px rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 4px 20px rgba(74, 144, 226, 0.4), 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

/* 录音中的样式 */
.record-button.recording {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  animation: recording-pulse 1.5s ease-in-out infinite;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.record-button.recording:hover {
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
  animation: recording-pulse 1.5s ease-in-out infinite;
}

/* 录音时的波形动画增强效果 */
.record-button.recording {
  position: relative;
}

.record-button.recording::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: inherit;
  transform: translate(-50%, -50%);
  animation: wave-pulse 2s ease-in-out infinite;
}

/* 波形增强动画 */
@keyframes wave-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 录音脉冲动画 */
@keyframes recording-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 录音按钮图标 */
.record-button :deep(.icon-wrapper) {
  --icon-size: 2dvw;
}

/* 波形图标特殊样式 */
.record-button.recording :deep(.voice-wave-icon) {
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
  animation: icon-glow 2s ease-in-out infinite;
}

@keyframes icon-glow {
  0%, 100% {
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 1));
  }
}

/* 确保波形图标在正确的层级 */
.record-button.recording :deep(.voice-wave-icon) {
  position: relative;
  z-index: 2;
}

/* 提示文字 */
.voice-tip {
  color: rgb(255, 179, 0);
  font-size: 0.7dvw;
  text-align: center;
  line-height: 1.4;
}

.settings-button{
    position: fixed;
    right: 1dvw;
    bottom: 1dvw;
    width: 2dvw;
    height: 2dvw;
}

.settings-button :deep(.icon-wrapper) {
  --icon-size: 1dvw;
}

/* 麦克风设置弹窗样式 */
.microphone-settings {
  padding: 12px 16px;
}

.settings-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.permission-status {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.status-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.device-selection {
  margin-bottom: 8px;
}

.device-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.device-info {
  margin-top: 8px;
  padding: 6px 0;
  text-align: center;
}

.no-device {
  margin-top: 8px;
  padding: 8px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  text-align: center;
}

.no-permission-tip {
  margin-bottom: 16px;
  padding: 8px;
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  text-align: center;
}

.refresh-section {
  text-align: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

/* Element Plus 组件样式覆盖 */
.microphone-settings .el-select {
  width: 100%;
}

.microphone-settings .el-button--small {
  font-size: 12px;
}