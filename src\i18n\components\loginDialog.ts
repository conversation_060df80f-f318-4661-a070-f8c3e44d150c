// LoginDialog 组件的国际化内容
export const loginDialog = {
  zh: {
    // 功能特性
    features: {
      diagnosis: '辅助鉴别诊断',
      voiceRecord: '语音病历录入',
      aiAssistant: 'AI知识助手',
      imageDiagnosis: '影像诊断助手'
    },
    
    // 应用信息
    appTitle: '好兽医AI助手',
    appDescription: '基于先进AI技术的专业兽医诊疗平台，为您提供全方位的智能化医疗服务',
    
    // 登录表单
    loginTitle: '登录后免费试用完整功能',
    phonePlaceholder: '请输入手机号',
    captchaPlaceholder: '请输入6位数字验证码',
    
    // 按钮文本
    getCaptchaButton: '获取验证码',
    sendingCaptcha: '发送中...',
    resendCaptcha: '秒后重新获取',
    loginButton: '登录',
    loggingIn: '登录中...',
    
    // 用户协议
    agreementText: '我已阅读并同意',
    userAgreement: '《用户协议》',
    privacyPolicy: '《隐私政策》',
    agreementConnector: '和',
    
    // 验证消息
    validation: {
      phoneRequired: '请输入正确的手机号',
      agreementRequired: '请先同意用户协议和隐私政策',
      captchaFailed: '获取验证码失败'
    }
  },
  
  en: {
    // 功能特性
    features: {
      diagnosis: 'Diagnostic Assistance',
      voiceRecord: 'Voice Medical Record',
      aiAssistant: 'AI Knowledge Assistant',
      imageDiagnosis: 'Image Diagnosis Assistant'
    },
    
    // 应用信息
    appTitle: 'Vet AI Assistant',
    appDescription: 'Professional veterinary diagnosis platform based on advanced AI technology, providing comprehensive intelligent medical services',
    
    // 登录表单
    loginTitle: 'Login for Free Trial of Full Features',
    phonePlaceholder: 'Please enter phone number',
    captchaPlaceholder: 'Please enter 6-digit verification code',
    
    // 按钮文本
    getCaptchaButton: 'Get Code',
    sendingCaptcha: 'Sending...',
    resendCaptcha: 'seconds to resend',
    loginButton: 'Login',
    loggingIn: 'Logging in...',
    
    // 用户协议
    agreementText: 'I have read and agree to',
    userAgreement: 'User Agreement',
    privacyPolicy: 'Privacy Policy',
    agreementConnector: 'and',
    
    // 验证消息
    validation: {
      phoneRequired: 'Please enter a valid phone number',
      agreementRequired: 'Please agree to the User Agreement and Privacy Policy first',
      captchaFailed: 'Failed to get verification code'
    }
  },
  
  ja: {
    // 功能特性
    features: {
      diagnosis: '診断支援',
      voiceRecord: '音声医療記録',
      aiAssistant: 'AI知識アシスタント',
      imageDiagnosis: '画像診断アシスタント'
    },
    
    // 应用信息
    appTitle: '獣医AIアシスタント',
    appDescription: '先進的なAI技術に基づく専門的な獣医診療プラットフォーム、包括的なインテリジェント医療サービスを提供',
    
    // 登录表单
    loginTitle: 'ログインして全機能を無料でお試しください',
    phonePlaceholder: '電話番号を入力してください',
    captchaPlaceholder: '6桁の認証コードを入力してください',
    
    // 按钮文本
    getCaptchaButton: '認証コード取得',
    sendingCaptcha: '送信中...',
    resendCaptcha: '秒後に再送信',
    loginButton: 'ログイン',
    loggingIn: 'ログイン中...',
    
    // 用户协议
    agreementText: '私は読んで同意しました',
    userAgreement: '利用規約',
    privacyPolicy: 'プライバシーポリシー',
    agreementConnector: 'と',
    
    // 验证消息
    validation: {
      phoneRequired: '正しい電話番号を入力してください',
      agreementRequired: '先に利用規約とプライバシーポリシーに同意してください',
      captchaFailed: '認証コードの取得に失敗しました'
    }
  },
  
  ko: {
    // 功能特性
    features: {
      diagnosis: '진단 지원',
      voiceRecord: '음성 의료 기록',
      aiAssistant: 'AI 지식 어시스턴트',
      imageDiagnosis: '영상 진단 어시스턴트'
    },
    
    // 应用信息
    appTitle: '수의사 AI 어시스턴트',
    appDescription: '첨단 AI 기술을 기반으로 한 전문 수의학 진료 플랫폼, 포괄적인 지능형 의료 서비스 제공',
    
    // 登录表单
    loginTitle: '로그인하여 전체 기능을 무료로 체험하세요',
    phonePlaceholder: '전화번호를 입력해 주세요',
    captchaPlaceholder: '6자리 인증번호를 입력해 주세요',
    
    // 按钮文本
    getCaptchaButton: '인증번호 받기',
    sendingCaptcha: '전송 중...',
    resendCaptcha: '초 후 재전송',
    loginButton: '로그인',
    loggingIn: '로그인 중...',
    
    // 用户协议
    agreementText: '저는 다음을 읽고 동의합니다',
    userAgreement: '이용약관',
    privacyPolicy: '개인정보처리방침',
    agreementConnector: '및',
    
    // 验证消息
    validation: {
      phoneRequired: '올바른 전화번호를 입력해 주세요',
      agreementRequired: '먼저 이용약관과 개인정보처리방침에 동의해 주세요',
      captchaFailed: '인증번호 받기에 실패했습니다'
    }
  },
  
  th: {
    features: {
      diagnosis: 'ความช่วยเหลือในการวินิจฉัย',
      voiceRecord: 'บันทึกการแพทย์ด้วยเสียง',
      aiAssistant: 'ผู้ช่วยความรู้ AI',
      imageDiagnosis: 'ผู้ช่วยวินิจฉัยภาพ'
    },
    appTitle: 'ผู้ช่วย AI สำหรับสัตวแพทย์',
    appDescription: 'แพลตฟอร์มการวินิจฉัยสัตวแพทย์มืออาชีพที่ใช้เทคโนโลยี AI ขั้นสูง ให้บริการทางการแพทย์อันชาญฉลาดอย่างครอบคลุม',
    loginTitle: 'เข้าสู่ระบบเพื่อทดลองใช้ฟีเจอร์ทั้งหมดฟรี',
    phonePlaceholder: 'กรุณาใส่หมายเลขโทรศัพท์',
    captchaPlaceholder: 'กรุณาใส่รหัสยืนยัน 6 หลัก',
    getCaptchaButton: 'รับรหัสยืนยัน',
    sendingCaptcha: 'กำลังส่ง...',
    resendCaptcha: 'วินาทีก่อนส่งใหม่',
    loginButton: 'เข้าสู่ระบบ',
    loggingIn: 'กำลังเข้าสู่ระบบ...',
    agreementText: 'ฉันได้อ่านและยอมรับ',
    userAgreement: 'ข้อตกลงผู้ใช้',
    privacyPolicy: 'นโยบายความเป็นส่วนตัว',
    agreementConnector: 'และ',
    validation: {
      phoneRequired: 'กรุณาใส่หมายเลขโทรศัพท์ที่ถูกต้อง',
      agreementRequired: 'กรุณายอมรับข้อตกลงผู้ใช้และนโยบายความเป็นส่วนตัวก่อน',
      captchaFailed: 'การรับรหัสยืนยันล้มเหลว'
    }
  },
  
  ms: {
    features: {
      diagnosis: 'Bantuan Diagnosis',
      voiceRecord: 'Rekod Perubatan Suara',
      aiAssistant: 'Pembantu Pengetahuan AI',
      imageDiagnosis: 'Pembantu Diagnosis Imej'
    },
    appTitle: 'Pembantu AI Veterinar',
    appDescription: 'Platform diagnosis veterinar profesional berdasarkan teknologi AI termaju, menyediakan perkhidmatan perubatan pintar yang komprehensif',
    loginTitle: 'Log masuk untuk Percubaan Percuma Ciri Penuh',
    phonePlaceholder: 'Sila masukkan nombor telefon',
    captchaPlaceholder: 'Sila masukkan kod pengesahan 6 digit',
    getCaptchaButton: 'Dapatkan Kod',
    sendingCaptcha: 'Menghantar...',
    resendCaptcha: 'saat untuk hantar semula',
    loginButton: 'Log Masuk',
    loggingIn: 'Sedang log masuk...',
    agreementText: 'Saya telah membaca dan bersetuju dengan',
    userAgreement: 'Perjanjian Pengguna',
    privacyPolicy: 'Dasar Privasi',
    agreementConnector: 'dan',
    validation: {
      phoneRequired: 'Sila masukkan nombor telefon yang sah',
      agreementRequired: 'Sila bersetuju dengan Perjanjian Pengguna dan Dasar Privasi terlebih dahulu',
      captchaFailed: 'Gagal mendapatkan kod pengesahan'
    }
  },
  
  id: {
    features: {
      diagnosis: 'Bantuan Diagnosis',
      voiceRecord: 'Rekam Medis Suara',
      aiAssistant: 'Asisten Pengetahuan AI',
      imageDiagnosis: 'Asisten Diagnosis Gambar'
    },
    appTitle: 'Asisten AI Veteriner',
    appDescription: 'Platform diagnosis veteriner profesional berbasis teknologi AI canggih, menyediakan layanan medis cerdas yang komprehensif',
    loginTitle: 'Masuk untuk Uji Coba Gratis Fitur Lengkap',
    phonePlaceholder: 'Mohon masukkan nomor telepon',
    captchaPlaceholder: 'Mohon masukkan kode verifikasi 6 digit',
    getCaptchaButton: 'Dapatkan Kode',
    sendingCaptcha: 'Mengirim...',
    resendCaptcha: 'detik untuk kirim ulang',
    loginButton: 'Masuk',
    loggingIn: 'Sedang masuk...',
    agreementText: 'Saya telah membaca dan menyetujui',
    userAgreement: 'Perjanjian Pengguna',
    privacyPolicy: 'Kebijakan Privasi',
    agreementConnector: 'dan',
    validation: {
      phoneRequired: 'Mohon masukkan nomor telepon yang valid',
      agreementRequired: 'Mohon setujui Perjanjian Pengguna dan Kebijakan Privasi terlebih dahulu',
      captchaFailed: 'Gagal mendapatkan kode verifikasi'
    }
  },
  
  vi: {
    features: {
      diagnosis: 'Hỗ trợ Chẩn đoán',
      voiceRecord: 'Hồ sơ Y tế Giọng nói',
      aiAssistant: 'Trợ lý Kiến thức AI',
      imageDiagnosis: 'Trợ lý Chẩn đoán Hình ảnh'
    },
    appTitle: 'Trợ lý AI Thú y',
    appDescription: 'Nền tảng chẩn đoán thú y chuyên nghiệp dựa trên công nghệ AI tiên tiến, cung cấp dịch vụ y tế thông minh toàn diện',
    loginTitle: 'Đăng nhập để Dùng thử Miễn phí Tính năng Đầy đủ',
    phonePlaceholder: 'Vui lòng nhập số điện thoại',
    captchaPlaceholder: 'Vui lòng nhập mã xác thực 6 chữ số',
    getCaptchaButton: 'Lấy Mã',
    sendingCaptcha: 'Đang gửi...',
    resendCaptcha: 'giây để gửi lại',
    loginButton: 'Đăng nhập',
    loggingIn: 'Đang đăng nhập...',
    agreementText: 'Tôi đã đọc và đồng ý với',
    userAgreement: 'Thỏa thuận Người dùng',
    privacyPolicy: 'Chính sách Quyền riêng tư',
    agreementConnector: 'và',
    validation: {
      phoneRequired: 'Vui lòng nhập số điện thoại hợp lệ',
      agreementRequired: 'Vui lòng đồng ý với Thỏa thuận Người dùng và Chính sách Quyền riêng tư trước',
      captchaFailed: 'Lấy mã xác thực thất bại'
    }
  }
};