<script setup lang="ts">
import { type UploadedFileInfo, type VoiceMedicalRecord } from '@/services/voiceService';
import { useMedicalRecordStore } from '@/stores/medicalRecord';
import { TokenCookieManager } from "@/utils/cookieUtils";
import { ElMessage, type UploadProps, type UploadUserFile } from 'element-plus';
import { debounce } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

// 国际化
const { t } = useI18n();

// 全局的Pina库
const medicalRecordStore = useMedicalRecordStore();

// 全局的响应数据
const {
    medicalRecord
} = storeToRefs(medicalRecordStore);

// 全局的方法
const {
    updateVoiceMedicalRecord
} = medicalRecordStore;

// 本组件的响应数据
const loading = ref(false)
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const inspectionReportFiles = ref<UploadUserFile[]>([])
const imageReportFiles = ref<UploadUserFile[]>([])
const activeNames = ref(['1','2','3','4','5'])

// 本组件的方法
const handleFormChange = debounce(async (newValue: VoiceMedicalRecord, _oldValue: VoiceMedicalRecord): Promise<void> => {
    try {
        // 检查是否有有效数据
        if (!newValue) {
            console.warn(t('voiceMedicalRecord.messages.updateWarning'));
            return;
        }
        // 设置加载状态
        loading.value = true;
        await updateVoiceMedicalRecord(newValue);
    } catch (error) {
        console.error(t('voiceMedicalRecord.messages.updateError'), error);
        ElMessage.error(t('voiceMedicalRecord.messages.updateFailed'));
    } finally {
        // 清除加载状态
        loading.value = false;
    }
}, 1000);
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
    dialogImageUrl.value = uploadFile.url!
    dialogVisible.value = true
}
const handleInspectionReportRemove: UploadProps['onRemove'] = (_uploadFile, uploadFiles) => {
    const inspectionReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        inspectionReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, inspection_report_ids: inspectionReportIds }
}
const handleImageReportRemove: UploadProps['onRemove'] = (_uploadFile, uploadFiles) => {
    const imageReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        imageReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, image_report_ids: imageReportIds }
}
const handleInspectionReportSuccess: UploadProps['onSuccess'] = (_response, _uploadFile, uploadFiles) => {
    const inspectionReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        inspectionReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, inspection_report_ids: inspectionReportIds }
}

const handleImageReportSuccess: UploadProps['onSuccess'] = (_response, _uploadFile, uploadFiles) => {
    const imageReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        imageReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, image_report_ids: imageReportIds }
}

// 本组件的监听器
watch(
    medicalRecord,
    (newValue, oldValue) => {
        if (newValue && oldValue) {
            handleFormChange(newValue, oldValue);
        }
    },
    {
        deep: true,
        immediate: false
    }
);

onMounted(() => {
    const inspectionReportList = medicalRecord.value?.inspection_report_files
    const imageReportList = medicalRecord.value?.image_report_files
    if (inspectionReportList && inspectionReportList.length > 0) {
        inspectionReportFiles.value = inspectionReportList.map((file: UploadedFileInfo) => {
            return {
                uid: file.file_id,
                url: file.url,
                name: file.cos_path.slice(file.cos_path.lastIndexOf('/') + 1, file.cos_path.lastIndexOf('.')),
                status: 'success'
            }
        })
    }
    if (imageReportList && imageReportList.length > 0) {
        imageReportFiles.value = imageReportList.map((file: UploadedFileInfo) => {
            return {
                uid: file.file_id,
                url: file.url,
                name: file.cos_path.slice(file.cos_path.lastIndexOf('/') + 1, file.cos_path.lastIndexOf('.')),
                status: 'success'
            }
        })
    }


});

onUnmounted(() => {
    handleFormChange.cancel();
});

</script>

<template>
    <div class="collapse-layout">
        <el-form v-loading="loading" :model="medicalRecord" label-width="auto" label-position="top"
            class="collapse-form">
            <el-collapse class="collapse-container" v-model="activeNames">
                <el-collapse-item :title="t('voiceMedicalRecord.sections.basicInfo')" name="1">
                    <div class="collapse-base-info">
                        <el-form-item :label="t('voiceMedicalRecord.basic.petName')" prop="pet_name">
                            <el-input v-model="medicalRecord!.pet_name" :placeholder="t('voiceMedicalRecord.basic.petNamePlaceholder')" />
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.basic.gender')" prop="pet_gender">
                            <el-radio-group v-model="medicalRecord!.pet_gender">
                                <el-radio value="MALE">{{ t('voiceMedicalRecord.basic.genderOptions.male') }}</el-radio>
                                <el-radio value="FEMALE">{{ t('voiceMedicalRecord.basic.genderOptions.female') }}</el-radio>
                                <el-radio value="UNKNOWN">{{ t('voiceMedicalRecord.basic.genderOptions.unknown') }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.basic.age')" placeholder="请输入宠物名的名称" prop="pet_name">
                            <el-input-number :min="0" class="pet-age-input-number"
                                v-model="medicalRecord!.pet_age_years" :controls="false" />
                            <span class="pet-age-title">{{ t('voiceMedicalRecord.basic.years') }}</span>
                            <el-input-number :min="0" class="pet-age-input-number"
                                v-model="medicalRecord!.pet_age_months" :controls="false" />
                            <span class="pet-age-title">{{ t('voiceMedicalRecord.basic.months') }}</span>
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.basic.neuterStatus')" prop="pet_is_neutered">
                            <el-radio-group v-model="medicalRecord!.pet_is_neutered">
                                <el-radio value="YES">{{ t('voiceMedicalRecord.basic.neuterOptions.yes') }}</el-radio>
                                <el-radio value="NO">{{ t('voiceMedicalRecord.basic.neuterOptions.no') }}</el-radio>
                                <el-radio value="UNKNOWN">{{ t('voiceMedicalRecord.basic.neuterOptions.unknown') }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.basic.species')" prop="pet_species">
                            <el-radio-group v-model="medicalRecord!.pet_species">
                                <el-radio value="DOG">{{ t('voiceMedicalRecord.basic.speciesOptions.dog') }}</el-radio>
                                <el-radio value="CAT">{{ t('voiceMedicalRecord.basic.speciesOptions.cat') }}</el-radio>
                                <el-radio value="OTHER">{{ t('voiceMedicalRecord.basic.speciesOptions.other') }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.basic.breed')" prop="pet_breed">
                            <el-input v-model="medicalRecord!.pet_breed" :placeholder="t('voiceMedicalRecord.basic.breedPlaceholder')" />
                        </el-form-item>
                    </div>
                </el-collapse-item>
                <el-collapse-item :title="t('voiceMedicalRecord.sections.subjectiveInfo')" name="2">
                    <el-form-item :label="t('voiceMedicalRecord.subjective.chiefComplaint')" prop="chief_complaint">
                        <el-input type="textarea" v-model="medicalRecord!.chief_complaint"
                            :autosize="{ minRows: 2, maxRows: 5 }" :placeholder="t('voiceMedicalRecord.subjective.chiefComplaintPlaceholder')" />
                    </el-form-item>
                    <el-form-item :label="t('voiceMedicalRecord.subjective.pastHistory')" prop="past_history">
                        <el-input type="textarea" v-model="medicalRecord!.past_history"
                            :autosize="{ minRows: 2, maxRows: 5 }" :placeholder="t('voiceMedicalRecord.subjective.pastHistoryPlaceholder')" />
                    </el-form-item>
                    <el-form-item :label="t('voiceMedicalRecord.subjective.presentIllness')" prop="present_illness">
                        <el-input type="textarea" v-model="medicalRecord!.present_illness"
                            :autosize="{ minRows: 2, maxRows: 5 }" :placeholder="t('voiceMedicalRecord.subjective.presentIllnessPlaceholder')" />
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item :title="t('voiceMedicalRecord.sections.physicalExam')" name="3">
                    <div class="collapse-body-info">
                        <el-form-item :label="t('voiceMedicalRecord.physical.weight')" prop="pet_weight">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_weight" controls-position="right"
                                :precision="2" class="pet-body-input-number" />
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.physical.temperature')" prop="pet_temperature">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_temperature" controls-position="right"
                                :precision="2" :step="0.1" class="pet-body-input-number" />
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.physical.heartRate')" prop="pet_heart_rate">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_heart_rate" controls-position="right"
                                class="pet-body-input-number" />
                        </el-form-item>
                        <el-form-item :label="t('voiceMedicalRecord.physical.respiratoryRate')" prop="pet_respiratory_rate">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_respiratory_rate"
                                controls-position="right" class="pet-body-input-number" />
                        </el-form-item>
                    </div>
                    <el-form-item :label="t('voiceMedicalRecord.physical.bodyDescription')" prop="body_desc">
                        <el-input type="textarea" v-model="medicalRecord!.body_desc"
                            :autosize="{ minRows: 2, maxRows: 5 }" :placeholder="t('voiceMedicalRecord.physical.bodyDescriptionPlaceholder')" />
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item :title="t('voiceMedicalRecord.sections.objectiveExam')" name="4">
                    <el-form-item :label="t('voiceMedicalRecord.objective.inspectionReport')" prop="inspection_report_ids">
                        <el-upload :multiple="true" v-model:file-list="inspectionReportFiles"
                            action="/api/voice-medical-records/upload_image"
                            :headers="{ Authorization: 'Bearer ' + TokenCookieManager.getToken() }"
                            list-type="picture-card" :on-preview="handlePictureCardPreview"
                            :on-remove="handleInspectionReportRemove" :on-success="handleInspectionReportSuccess">
                            <el-icon>
                                <Plus />
                            </el-icon>
                        </el-upload>
                    </el-form-item>
                    <el-form-item :label="t('voiceMedicalRecord.objective.imageReport')" prop="image_report_ids">
                        <el-upload :multiple="true" v-model:file-list="imageReportFiles"
                            action="/api/voice-medical-records/upload_image"
                            :headers="{ Authorization: 'Bearer ' + TokenCookieManager.getToken() }"
                            list-type="picture-card" :on-preview="handlePictureCardPreview"
                            :on-remove="handleImageReportRemove" :on-success="handleImageReportSuccess">
                            <el-icon>
                                <Plus />
                            </el-icon>
                        </el-upload>
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item :title="t('voiceMedicalRecord.sections.diagnosisTreatment')" name="5">
                    <el-form-item :label="t('voiceMedicalRecord.diagnosis.diagnosis')" prop="diagnosis">
                        <el-input type="textarea" v-model="medicalRecord!.diagnosis"
                            :autosize="{ minRows: 2, maxRows: 5 }" :placeholder="t('voiceMedicalRecord.diagnosis.diagnosisPlaceholder')" />
                    </el-form-item>
                    <el-form-item :label="t('voiceMedicalRecord.diagnosis.treatment')" prop="treatment">
                        <el-input type="textarea" v-model="medicalRecord!.treatment"
                            :autosize="{ minRows: 2, maxRows: 5 }" :placeholder="t('voiceMedicalRecord.diagnosis.treatmentPlaceholder')" />
                    </el-form-item>
                    <el-form-item :label="t('voiceMedicalRecord.diagnosis.advice')" prop="advice">
                        <el-input type="textarea" v-model="medicalRecord!.advice" :autosize="{ minRows: 2, maxRows: 5 }"
                            :placeholder="t('voiceMedicalRecord.diagnosis.advicePlaceholder')" />
                    </el-form-item>
                </el-collapse-item>
            </el-collapse>
        </el-form>
        <el-dialog v-model="dialogVisible">
            <img w-full :src="dialogImageUrl" alt="Preview Image" />
        </el-dialog>
    </div>
</template>

<style scoped src="./VoiceMedicalRecord.css"></style>