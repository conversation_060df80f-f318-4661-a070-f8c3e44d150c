export const voiceMedicalRecord = {
  zh: {
    sections: {
      basicInfo: '基本信息',
      subjectiveInfo: '主观信息',
      physicalExam: '体格检查',
      objectiveExam: '客观检查',
      diagnosisTreatment: '诊断与治疗方案'
    },
    basic: {
      petName: '宠物名',
      petNamePlaceholder: '请输入宠物的名字',
      gender: '性别',
      genderOptions: {
        male: '公',
        female: '母',
        unknown: '未知'
      },
      age: '年龄',
      years: '年',
      months: '月',
      neuterStatus: '绝育状态',
      neuterOptions: {
        yes: '已绝育',
        no: '未绝育',
        unknown: '未知'
      },
      species: '种类',
      speciesOptions: {
        dog: '犬',
        cat: '猫',
        other: '其他'
      },
      breed: '品种',
      breedPlaceholder: '请输入宠物的品种'
    },
    subjective: {
      chiefComplaint: '主诉',
      chiefComplaintPlaceholder: '请输入宠物的主诉',
      pastHistory: '既往病史',
      pastHistoryPlaceholder: '请输入宠物的既往病史',
      presentIllness: '现病史',
      presentIllnessPlaceholder: '请输入宠物的现病史'
    },
    physical: {
      weight: '体重 (kg)',
      temperature: '体温 (°C)',
      heartRate: '心率 (次/分)',
      respiratoryRate: '呼吸频率 (次/分)',
      bodyDescription: '体况描述',
      bodyDescriptionPlaceholder: '请输入宠物的体况描述'
    },
    objective: {
      inspectionReport: '检查检验报告',
      imageReport: '影像报告'
    },
    diagnosis: {
      diagnosis: '诊断',
      diagnosisPlaceholder: '请输入诊断内容',
      treatment: '治疗方案',
      treatmentPlaceholder: '请输入治疗方案',
      advice: '医嘱',
      advicePlaceholder: '请输入医嘱'
    },
    messages: {
      updateFailed: '保存失败，请稍后重试',
      updateWarning: '表单数据为空，跳过更新',
      updateError: '更新医疗记录失败:'
    }
  },
  en: {
    sections: {
      basicInfo: 'Basic Information',
      subjectiveInfo: 'Subjective Information',
      physicalExam: 'Physical Examination',
      objectiveExam: 'Objective Examination',
      diagnosisTreatment: 'Diagnosis & Treatment Plan'
    },
    basic: {
      petName: 'Pet Name',
      petNamePlaceholder: 'Enter pet name',
      gender: 'Gender',
      genderOptions: {
        male: 'Male',
        female: 'Female',
        unknown: 'Unknown'
      },
      age: 'Age',
      years: 'Years',
      months: 'Months',
      neuterStatus: 'Neuter Status',
      neuterOptions: {
        yes: 'Neutered',
        no: 'Not Neutered',
        unknown: 'Unknown'
      },
      species: 'Species',
      speciesOptions: {
        dog: 'Dog',
        cat: 'Cat',
        other: 'Other'
      },
      breed: 'Breed',
      breedPlaceholder: 'Enter pet breed'
    },
    subjective: {
      chiefComplaint: 'Chief Complaint',
      chiefComplaintPlaceholder: 'Enter chief complaint',
      pastHistory: 'Past Medical History',
      pastHistoryPlaceholder: 'Enter past medical history',
      presentIllness: 'Present Illness',
      presentIllnessPlaceholder: 'Enter present illness history'
    },
    physical: {
      weight: 'Weight (kg)',
      temperature: 'Temperature (°C)',
      heartRate: 'Heart Rate (bpm)',
      respiratoryRate: 'Respiratory Rate (bpm)',
      bodyDescription: 'Body Condition',
      bodyDescriptionPlaceholder: 'Enter body condition description'
    },
    objective: {
      inspectionReport: 'Laboratory Reports',
      imageReport: 'Imaging Reports'
    },
    diagnosis: {
      diagnosis: 'Diagnosis',
      diagnosisPlaceholder: 'Enter diagnosis',
      treatment: 'Treatment Plan',
      treatmentPlaceholder: 'Enter treatment plan',
      advice: 'Medical Advice',
      advicePlaceholder: 'Enter medical advice'
    },
    messages: {
      updateFailed: 'Save failed, please try again later',
      updateWarning: 'Form data is empty, skipping update',
      updateError: 'Failed to update medical record:'
    }
  },
  ja: {
    sections: {
      basicInfo: '基本情報',
      subjectiveInfo: '主観的情報',
      physicalExam: '身体検査',
      objectiveExam: '客観的検査',
      diagnosisTreatment: '診断・治療計画'
    },
    basic: {
      petName: 'ペット名',
      petNamePlaceholder: 'ペットの名前を入力してください',
      gender: '性別',
      genderOptions: {
        male: 'オス',
        female: 'メス',
        unknown: '不明'
      },
      age: '年齢',
      years: '歳',
      months: 'ヶ月',
      neuterStatus: '去勢・避妊状態',
      neuterOptions: {
        yes: '済み',
        no: '未実施',
        unknown: '不明'
      },
      species: '種類',
      speciesOptions: {
        dog: '犬',
        cat: '猫',
        other: 'その他'
      },
      breed: '品種',
      breedPlaceholder: 'ペットの品種を入力してください'
    },
    subjective: {
      chiefComplaint: '主訴',
      chiefComplaintPlaceholder: 'ペットの主訴を入力してください',
      pastHistory: '既往歴',
      pastHistoryPlaceholder: 'ペットの既往歴を入力してください',
      presentIllness: '現病歴',
      presentIllnessPlaceholder: 'ペットの現病歴を入力してください'
    },
    physical: {
      weight: '体重 (kg)',
      temperature: '体温 (°C)',
      heartRate: '心拍数 (回/分)',
      respiratoryRate: '呼吸数 (回/分)',
      bodyDescription: '体況',
      bodyDescriptionPlaceholder: 'ペットの体況を入力してください'
    },
    objective: {
      inspectionReport: '検査レポート',
      imageReport: '画像レポート'
    },
    diagnosis: {
      diagnosis: '診断',
      diagnosisPlaceholder: '診断内容を入力してください',
      treatment: '治療計画',
      treatmentPlaceholder: '治療計画を入力してください',
      advice: '医師の指示',
      advicePlaceholder: '医師の指示を入力してください'
    },
    messages: {
      updateFailed: '保存に失敗しました。後でもう一度お試しください',
      updateWarning: 'フォームデータが空です。更新をスキップします',
      updateError: '医療記録の更新に失敗しました:'
    }
  },
  ko: {
    sections: {
      basicInfo: '기본 정보',
      subjectiveInfo: '주관적 정보',
      physicalExam: '신체 검사',
      objectiveExam: '객관적 검사',
      diagnosisTreatment: '진단 및 치료 계획'
    },
    basic: {
      petName: '반려동물 이름',
      petNamePlaceholder: '반려동물의 이름을 입력하세요',
      gender: '성별',
      genderOptions: {
        male: '수컷',
        female: '암컷',
        unknown: '알 수 없음'
      },
      age: '나이',
      years: '년',
      months: '개월',
      neuterStatus: '중성화 상태',
      neuterOptions: {
        yes: '중성화됨',
        no: '중성화 안됨',
        unknown: '알 수 없음'
      },
      species: '종류',
      speciesOptions: {
        dog: '강아지',
        cat: '고양이',
        other: '기타'
      },
      breed: '품종',
      breedPlaceholder: '반려동물의 품종을 입력하세요'
    },
    subjective: {
      chiefComplaint: '주요 증상',
      chiefComplaintPlaceholder: '반려동물의 주요 증상을 입력하세요',
      pastHistory: '과거 병력',
      pastHistoryPlaceholder: '반려동물의 과거 병력을 입력하세요',
      presentIllness: '현재 병력',
      presentIllnessPlaceholder: '반려동물의 현재 병력을 입력하세요'
    },
    physical: {
      weight: '체중 (kg)',
      temperature: '체온 (°C)',
      heartRate: '심박수 (회/분)',
      respiratoryRate: '호흡수 (회/분)',
      bodyDescription: '신체 상태',
      bodyDescriptionPlaceholder: '반려동물의 신체 상태를 입력하세요'
    },
    objective: {
      inspectionReport: '검사 보고서',
      imageReport: '영상 보고서'
    },
    diagnosis: {
      diagnosis: '진단',
      diagnosisPlaceholder: '진단 내용을 입력하세요',
      treatment: '치료 계획',
      treatmentPlaceholder: '치료 계획을 입력하세요',
      advice: '의학적 조언',
      advicePlaceholder: '의학적 조언을 입력하세요'
    },
    messages: {
      updateFailed: '저장에 실패했습니다. 나중에 다시 시도해 주세요',
      updateWarning: '폼 데이터가 비어있습니다. 업데이트를 건너뜁니다',
      updateError: '의료 기록 업데이트에 실패했습니다:'
    }
  },
  th: {
    sections: {
      basicInfo: 'ข้อมูลพื้นฐาน',
      subjectiveInfo: 'ข้อมูลจากการสังเกต',
      physicalExam: 'การตรวจร่างกาย',
      objectiveExam: 'การตรวจเชิงวัตถุ',
      diagnosisTreatment: 'การวินิจฉัยและแผนการรักษา'
    },
    basic: {
      petName: 'ชื่อสัตว์เลี้ยง',
      petNamePlaceholder: 'กรุณากรอกชื่อสัตว์เลี้ยง',
      gender: 'เพศ',
      genderOptions: {
        male: 'ตัวผู้',
        female: 'ตัวเมีย',
        unknown: 'ไม่ทราบ'
      },
      age: 'อายุ',
      years: 'ปี',
      months: 'เดือน',
      neuterStatus: 'สถานะการทำหมัน',
      neuterOptions: {
        yes: 'ทำหมันแล้ว',
        no: 'ยังไม่ทำหมัน',
        unknown: 'ไม่ทราบ'
      },
      species: 'ชนิด',
      speciesOptions: {
        dog: 'สุนัข',
        cat: 'แมว',
        other: 'อื่นๆ'
      },
      breed: 'สายพันธุ์',
      breedPlaceholder: 'กรุณากรอกสายพันธุ์ของสัตว์เลี้ยง'
    },
    subjective: {
      chiefComplaint: 'อาการหลัก',
      chiefComplaintPlaceholder: 'กรุณากรอกอาการหลักของสัตว์เลี้ยง',
      pastHistory: 'ประวัติการเจ็บป่วย',
      pastHistoryPlaceholder: 'กรุณากรอกประวัติการเจ็บป่วยของสัตว์เลี้ยง',
      presentIllness: 'อาการปัจจุบัน',
      presentIllnessPlaceholder: 'กรุณากรอกอาการปัจจุบันของสัตว์เลี้ยง'
    },
    physical: {
      weight: 'น้ำหนัก (กก.)',
      temperature: 'อุณหภูมิร่างกาย (°C)',
      heartRate: 'อัตราการเต้นของหัวใจ (ครั้ง/นาที)',
      respiratoryRate: 'อัตราการหายใจ (ครั้ง/นาที)',
      bodyDescription: 'สภาพร่างกาย',
      bodyDescriptionPlaceholder: 'กรุณากรอกสภาพร่างกายของสัตว์เลี้ยง'
    },
    objective: {
      inspectionReport: 'รายงานการตรวจ',
      imageReport: 'รายงานภาพถ่าย'
    },
    diagnosis: {
      diagnosis: 'การวินิจฉัย',
      diagnosisPlaceholder: 'กรุณากรอกการวินิจฉัย',
      treatment: 'แผนการรักษา',
      treatmentPlaceholder: 'กรุณากรอกแผนการรักษา',
      advice: 'คำแนะนำทางการแพทย์',
      advicePlaceholder: 'กรุณากรอกคำแนะนำทางการแพทย์'
    },
    messages: {
      updateFailed: 'บันทึกล้มเหลว กรุณาลองอีกครั้งในภายหลัง',
      updateWarning: 'ข้อมูลฟอร์มว่างเปล่า ข้ามการอัปเดต',
      updateError: 'อัปเดตบันทึกทางการแพทย์ล้มเหลว:'
    }
  },
  ms: {
    sections: {
      basicInfo: 'Maklumat Asas',
      subjectiveInfo: 'Maklumat Subjektif',
      physicalExam: 'Pemeriksaan Fizikal',
      objectiveExam: 'Pemeriksaan Objektif',
      diagnosisTreatment: 'Diagnosis & Pelan Rawatan'
    },
    basic: {
      petName: 'Nama Haiwan Kesayangan',
      petNamePlaceholder: 'Masukkan nama haiwan kesayangan',
      gender: 'Jantina',
      genderOptions: {
        male: 'Jantan',
        female: 'Betina',
        unknown: 'Tidak Diketahui'
      },
      age: 'Umur',
      years: 'Tahun',
      months: 'Bulan',
      neuterStatus: 'Status Pemanduan',
      neuterOptions: {
        yes: 'Sudah Dimandulkan',
        no: 'Belum Dimandulkan',
        unknown: 'Tidak Diketahui'
      },
      species: 'Spesies',
      speciesOptions: {
        dog: 'Anjing',
        cat: 'Kucing',
        other: 'Lain-lain'
      },
      breed: 'Baka',
      breedPlaceholder: 'Masukkan baka haiwan kesayangan'
    },
    subjective: {
      chiefComplaint: 'Aduan Utama',
      chiefComplaintPlaceholder: 'Masukkan aduan utama haiwan kesayangan',
      pastHistory: 'Sejarah Perubatan Lepas',
      pastHistoryPlaceholder: 'Masukkan sejarah perubatan lepas haiwan kesayangan',
      presentIllness: 'Penyakit Semasa',
      presentIllnessPlaceholder: 'Masukkan sejarah penyakit semasa haiwan kesayangan'
    },
    physical: {
      weight: 'Berat (kg)',
      temperature: 'Suhu Badan (°C)',
      heartRate: 'Kadar Denyutan (denyutan/minit)',
      respiratoryRate: 'Kadar Pernafasan (nafas/minit)',
      bodyDescription: 'Keadaan Badan',
      bodyDescriptionPlaceholder: 'Masukkan penerangan keadaan badan haiwan kesayangan'
    },
    objective: {
      inspectionReport: 'Laporan Pemeriksaan',
      imageReport: 'Laporan Imej'
    },
    diagnosis: {
      diagnosis: 'Diagnosis',
      diagnosisPlaceholder: 'Masukkan diagnosis',
      treatment: 'Pelan Rawatan',
      treatmentPlaceholder: 'Masukkan pelan rawatan',
      advice: 'Nasihat Perubatan',
      advicePlaceholder: 'Masukkan nasihat perubatan'
    },
    messages: {
      updateFailed: 'Simpan gagal, sila cuba lagi kemudian',
      updateWarning: 'Data borang kosong, melangkau kemas kini',
      updateError: 'Gagal mengemaskini rekod perubatan:'
    }
  },
  id: {
    sections: {
      basicInfo: 'Informasi Dasar',
      subjectiveInfo: 'Informasi Subjektif',
      physicalExam: 'Pemeriksaan Fisik',
      objectiveExam: 'Pemeriksaan Objektif',
      diagnosisTreatment: 'Diagnosis & Rencana Perawatan'
    },
    basic: {
      petName: 'Nama Hewan Peliharaan',
      petNamePlaceholder: 'Masukkan nama hewan peliharaan',
      gender: 'Jenis Kelamin',
      genderOptions: {
        male: 'Jantan',
        female: 'Betina',
        unknown: 'Tidak Diketahui'
      },
      age: 'Umur',
      years: 'Tahun',
      months: 'Bulan',
      neuterStatus: 'Status Steril',
      neuterOptions: {
        yes: 'Sudah Disteril',
        no: 'Belum Disteril',
        unknown: 'Tidak Diketahui'
      },
      species: 'Spesies',
      speciesOptions: {
        dog: 'Anjing',
        cat: 'Kucing',
        other: 'Lainnya'
      },
      breed: 'Ras',
      breedPlaceholder: 'Masukkan ras hewan peliharaan'
    },
    subjective: {
      chiefComplaint: 'Keluhan Utama',
      chiefComplaintPlaceholder: 'Masukkan keluhan utama hewan peliharaan',
      pastHistory: 'Riwayat Penyakit Terdahulu',
      pastHistoryPlaceholder: 'Masukkan riwayat penyakit terdahulu hewan peliharaan',
      presentIllness: 'Riwayat Penyakit Sekarang',
      presentIllnessPlaceholder: 'Masukkan riwayat penyakit sekarang hewan peliharaan'
    },
    physical: {
      weight: 'Berat Badan (kg)',
      temperature: 'Suhu Tubuh (°C)',
      heartRate: 'Denyut Jantung (denyut/menit)',
      respiratoryRate: 'Frekuensi Napas (napas/menit)',
      bodyDescription: 'Kondisi Tubuh',
      bodyDescriptionPlaceholder: 'Masukkan deskripsi kondisi tubuh hewan peliharaan'
    },
    objective: {
      inspectionReport: 'Laporan Pemeriksaan',
      imageReport: 'Laporan Pencitraan'
    },
    diagnosis: {
      diagnosis: 'Diagnosis',
      diagnosisPlaceholder: 'Masukkan diagnosis',
      treatment: 'Rencana Perawatan',
      treatmentPlaceholder: 'Masukkan rencana perawatan',
      advice: 'Saran Medis',
      advicePlaceholder: 'Masukkan saran medis'
    },
    messages: {
      updateFailed: 'Simpan gagal, silakan coba lagi nanti',
      updateWarning: 'Data formulir kosong, melewati pembaruan',
      updateError: 'Gagal memperbarui catatan medis:'
    }
  },
  vi: {
    sections: {
      basicInfo: 'Thông tin Cơ bản',
      subjectiveInfo: 'Thông tin Chủ quan',
      physicalExam: 'Khám Thể chất',
      objectiveExam: 'Khám Khách quan',
      diagnosisTreatment: 'Chẩn đoán & Kế hoạch Điều trị'
    },
    basic: {
      petName: 'Tên Thú cưng',
      petNamePlaceholder: 'Nhập tên thú cưng',
      gender: 'Giới tính',
      genderOptions: {
        male: 'Đực',
        female: 'Cái',
        unknown: 'Không xác định'
      },
      age: 'Tuổi',
      years: 'Năm',
      months: 'Tháng',
      neuterStatus: 'Tình trạng Triệt sản',
      neuterOptions: {
        yes: 'Đã triệt sản',
        no: 'Chưa triệt sản',
        unknown: 'Không xác định'
      },
      species: 'Loài',
      speciesOptions: {
        dog: 'Chó',
        cat: 'Mèo',
        other: 'Khác'
      },
      breed: 'Giống',
      breedPlaceholder: 'Nhập giống thú cưng'
    },
    subjective: {
      chiefComplaint: 'Triệu chứng Chính',
      chiefComplaintPlaceholder: 'Nhập triệu chứng chính của thú cưng',
      pastHistory: 'Tiền sử Bệnh',
      pastHistoryPlaceholder: 'Nhập tiền sử bệnh của thú cưng',
      presentIllness: 'Bệnh sử Hiện tại',
      presentIllnessPlaceholder: 'Nhập bệnh sử hiện tại của thú cưng'
    },
    physical: {
      weight: 'Cân nặng (kg)',
      temperature: 'Nhiệt độ (°C)',
      heartRate: 'Nhịp tim (lần/phút)',
      respiratoryRate: 'Tần số hô hấp (lần/phút)',
      bodyDescription: 'Tình trạng Cơ thể',
      bodyDescriptionPlaceholder: 'Nhập mô tả tình trạng cơ thể thú cưng'
    },
    objective: {
      inspectionReport: 'Báo cáo Xét nghiệm',
      imageReport: 'Báo cáo Hình ảnh'
    },
    diagnosis: {
      diagnosis: 'Chẩn đoán',
      diagnosisPlaceholder: 'Nhập chẩn đoán',
      treatment: 'Kế hoạch Điều trị',
      treatmentPlaceholder: 'Nhập kế hoạch điều trị',
      advice: 'Lời khuyên Y tế',
      advicePlaceholder: 'Nhập lời khuyên y tế'
    },
    messages: {
      updateFailed: 'Lưu thất bại, vui lòng thử lại sau',
      updateWarning: 'Dữ liệu biểu mẫu trống, bỏ qua cập nhật',
      updateError: 'Cập nhật hồ sơ y tế thất bại:'
    }
  }
};