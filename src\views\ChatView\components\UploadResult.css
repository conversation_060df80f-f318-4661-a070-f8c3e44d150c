.upload-result-dialog {
  height: 75dvh;
  display: flex;
  gap: 0.6vw;
}

.upload-section {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 5;
  gap: 0.6vw;
  width: 0%;
}

.upload-content {
  overflow: auto;
  background: linear-gradient(145deg,
      rgba(135, 206, 235, 0.08) 0%,
      rgba(96, 151, 252, 0.05) 100%);
  border: 1px solid rgba(135, 206, 235, 0.2);
  border-radius: 0.5dvw;
  padding: 1dvw;
}

.upload-slider {
  overflow: auto;
  height: 100%;
  flex: 1;
}

.upload-file-list {
  list-style: none;
  padding: 12px;
  margin: 0;
  background: linear-gradient(145deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(135, 206, 235, 0.05) 100%);
  border-radius: 8px;
  border: 1px solid rgba(135, 206, 235, 0.2);
  backdrop-filter: blur(5px);
}

.upload-file-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(135, 206, 235, 0.03) 100%);
  border-radius: 6px;
  border: 1px solid rgba(135, 206, 235, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(5px);
}

.upload-file-item:last-child {
  margin-bottom: 0;
}

.upload-file-item:hover {
  border-color: rgba(135, 206, 235, 0.4);
  box-shadow: 0 2px 4px rgba(96, 151, 252, 0.15);
}

.upload-file-item.active {
  border-color: #6097fc;
  box-shadow: 0 2px 8px rgba(96, 151, 252, 0.25);
  background: linear-gradient(135deg,
      rgba(135, 206, 235, 0.1) 0%,
      rgba(96, 151, 252, 0.05) 100%);
}

.upload-file-image-container {
  position: relative;
  width: 100%;
}

.upload-file-image-options {
  opacity: 0;
  position: absolute;
  top: 4px;
  right: 4px;
  transition: opacity 0.3s ease;
}

.upload-file-item:hover .upload-file-image-options {
  opacity: 1;
}

.expand-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(135, 206, 235, 0.1) 100%);
  border: 1px solid rgba(135, 206, 235, 0.3);
  backdrop-filter: blur(5px);
}

.expand-button:hover {
  background: linear-gradient(135deg, #6097fc 0%, #87ceeb 100%);
  border-color: #6097fc;
  color: white;
  box-shadow: 0 2px 6px rgba(96, 151, 252, 0.2);
}

.delete-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(220, 53, 69, 0.1) 100%);
  border: 1px solid rgba(135, 206, 235, 0.3);
  backdrop-filter: blur(5px);
}

.delete-button:hover {
  background: linear-gradient(135deg, #dc3545 0%, #f56565 100%);
  border-color: #dc3545;
  color: white;
  box-shadow: 0 2px 6px rgba(220, 53, 69, 0.2);
}

.upload-file-title {
  text-align: center;
  width: 6dvw;
  display: block;
}

.upload-file-image {
  height: 13dvh;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.dialog-footer {
  padding-top: 1dvw;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 表格容器样式 */
.table-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.editable-table-wrapper {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(96, 151, 252, 0.1);
  transition: box-shadow 0.3s ease;
}

.editable-table-wrapper:hover {
  box-shadow: 0 4px 16px rgba(96, 151, 252, 0.15);
}

.editable-table-wrapper:last-child {
  margin-bottom: 0;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.markdown-content th,
.markdown-content td {
  padding: 12px 8px;
  text-align: left;
  border: 1px solid rgba(135, 206, 235, 0.2);
}

.markdown-content th {
  background: linear-gradient(135deg, #6097fc 0%, #87ceeb 100%);
  color: white;
  font-weight: 600;
}

.markdown-content tr:nth-child(even) {
  background-color: rgba(135, 206, 235, 0.03);
}

.markdown-content tr:hover {
  background-color: rgba(135, 206, 235, 0.08);
}