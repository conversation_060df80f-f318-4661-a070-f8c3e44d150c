// Feedback 组件的国际化内容
export const feedback = {
  zh: {
    // 对话框
    dialogTitle: '意见反馈',
    
    // 反馈类型
    feedbackTypeTitle: '反馈类型',
    feedbackTypes: {
      feature: '功能建议',
      design: '界面设计', 
      performance: '性能问题',
      other: '其他问题'
    },
    
    // 表单标题
    feedbackContentTitle: '反馈内容',
    contactInfoTitle: '联系方式',
    
    // 输入框提示
    feedbackPlaceholder: '请详细描述您的建议...',
    emailPlaceholder: '请输入邮箱地址',
    phonePlaceholder: '请输入手机号码',
    
    // 按钮
    cancelButton: '取消',
    submitButton: '提交反馈',
    
    // 验证消息
    validation: {
      feedbackRequired: '请输入反馈内容',
      feedbackLength: '反馈内容长度在 1 到 500 个字符',
      emailFormat: '请输入正确的邮箱地址',
      phoneFormat: '请输入正确的手机号码'
    }
  },
  
  en: {
    // 对话框
    dialogTitle: 'Feedback',
    
    // 反馈类型
    feedbackTypeTitle: 'Feedback Type',
    feedbackTypes: {
      feature: 'Feature Suggestion',
      design: 'Interface Design',
      performance: 'Performance Issue',
      other: 'Other Issues'
    },
    
    // 表单标题
    feedbackContentTitle: 'Feedback Content',
    contactInfoTitle: 'Contact Information',
    
    // 输入框提示
    feedbackPlaceholder: 'Please describe your suggestions in detail...',
    emailPlaceholder: 'Please enter email address',
    phonePlaceholder: 'Please enter phone number',
    
    // 按钮
    cancelButton: 'Cancel',
    submitButton: 'Submit Feedback',
    
    // 验证消息
    validation: {
      feedbackRequired: 'Please enter feedback content',
      feedbackLength: 'Feedback content length should be 1 to 500 characters',
      emailFormat: 'Please enter a valid email address',
      phoneFormat: 'Please enter a valid phone number'
    }
  },
  
  ja: {
    // 对话框
    dialogTitle: 'フィードバック',
    
    // 反馈类型
    feedbackTypeTitle: 'フィードバックタイプ',
    feedbackTypes: {
      feature: '機能提案',
      design: 'インターフェースデザイン',
      performance: 'パフォーマンス問題',
      other: 'その他の問題'
    },
    
    // 表单标题
    feedbackContentTitle: 'フィードバック内容',
    contactInfoTitle: '連絡先情報',
    
    // 输入框提示
    feedbackPlaceholder: 'ご提案を詳しく記述してください...',
    emailPlaceholder: 'メールアドレスを入力してください',
    phonePlaceholder: '電話番号を入力してください',
    
    // 按钮
    cancelButton: 'キャンセル',
    submitButton: 'フィードバック送信',
    
    // 验证消息
    validation: {
      feedbackRequired: 'フィードバック内容を入力してください',
      feedbackLength: 'フィードバック内容は1〜500文字で入力してください',
      emailFormat: '正しいメールアドレスを入力してください',
      phoneFormat: '正しい電話番号を入力してください'
    }
  },
  
  ko: {
    // 对话框
    dialogTitle: '피드백',
    
    // 反馈类型
    feedbackTypeTitle: '피드백 유형',
    feedbackTypes: {
      feature: '기능 제안',
      design: '인터페이스 디자인',
      performance: '성능 문제',
      other: '기타 문제'
    },
    
    // 表单标题
    feedbackContentTitle: '피드백 내용',
    contactInfoTitle: '연락처 정보',
    
    // 输入框提示
    feedbackPlaceholder: '제안사항을 자세히 설명해 주세요...',
    emailPlaceholder: '이메일 주소를 입력해 주세요',
    phonePlaceholder: '전화번호를 입력해 주세요',
    
    // 按钮
    cancelButton: '취소',
    submitButton: '피드백 제출',
    
    // 验证消息
    validation: {
      feedbackRequired: '피드백 내용을 입력해 주세요',
      feedbackLength: '피드백 내용은 1~500자로 입력해 주세요',
      emailFormat: '올바른 이메일 주소를 입력해 주세요',
      phoneFormat: '올바른 전화번호를 입력해 주세요'
    }
  },
  
  th: {
    dialogTitle: 'ข้อเสนอแนะ',
    feedbackTypeTitle: 'ประเภทข้อเสนอแนะ',
    feedbackTypes: {
      feature: 'ข้อเสนอแนะฟีเจอร์',
      design: 'การออกแบบอินเตอร์เฟซ',
      performance: 'ปัญหาประสิทธิภาพ',
      other: 'ปัญหาอื่นๆ'
    },
    feedbackContentTitle: 'เนื้อหาข้อเสนอแนะ',
    contactInfoTitle: 'ข้อมูลติดต่อ',
    feedbackPlaceholder: 'กรุณาอธิบายข้อเสนอแนะของคุณโดยละเอียด...',
    emailPlaceholder: 'กรุณาใส่ที่อยู่อีเมล',
    phonePlaceholder: 'กรุณาใส่หมายเลขโทรศัพท์',
    cancelButton: 'ยกเลิก',
    submitButton: 'ส่งข้อเสนอแนะ',
    validation: {
      feedbackRequired: 'กรุณาใส่เนื้อหาข้อเสนอแนะ',
      feedbackLength: 'เนื้อหาข้อเสนอแนะควรมีความยาว 1 ถึง 500 ตัวอักษร',
      emailFormat: 'กรุณาใส่ที่อยู่อีเมลที่ถูกต้อง',
      phoneFormat: 'กรุณาใส่หมายเลขโทรศัพท์ที่ถูกต้อง'
    }
  },
  
  ms: {
    dialogTitle: 'Maklum Balas',
    feedbackTypeTitle: 'Jenis Maklum Balas',
    feedbackTypes: {
      feature: 'Cadangan Ciri',
      design: 'Reka Bentuk Antara Muka',
      performance: 'Isu Prestasi',
      other: 'Isu Lain'
    },
    feedbackContentTitle: 'Kandungan Maklum Balas',
    contactInfoTitle: 'Maklumat Hubungan',
    feedbackPlaceholder: 'Sila huraikan cadangan anda secara terperinci...',
    emailPlaceholder: 'Sila masukkan alamat emel',
    phonePlaceholder: 'Sila masukkan nombor telefon',
    cancelButton: 'Batal',
    submitButton: 'Hantar Maklum Balas',
    validation: {
      feedbackRequired: 'Sila masukkan kandungan maklum balas',
      feedbackLength: 'Panjang kandungan maklum balas hendaklah 1 hingga 500 aksara',
      emailFormat: 'Sila masukkan alamat emel yang sah',
      phoneFormat: 'Sila masukkan nombor telefon yang sah'
    }
  },
  
  id: {
    dialogTitle: 'Umpan Balik',
    feedbackTypeTitle: 'Jenis Umpan Balik',
    feedbackTypes: {
      feature: 'Saran Fitur',
      design: 'Desain Antarmuka',
      performance: 'Masalah Kinerja',
      other: 'Masalah Lainnya'
    },
    feedbackContentTitle: 'Konten Umpan Balik',
    contactInfoTitle: 'Informasi Kontak',
    feedbackPlaceholder: 'Mohon jelaskan saran Anda secara detail...',
    emailPlaceholder: 'Mohon masukkan alamat email',
    phonePlaceholder: 'Mohon masukkan nomor telepon',
    cancelButton: 'Batal',
    submitButton: 'Kirim Umpan Balik',
    validation: {
      feedbackRequired: 'Mohon masukkan konten umpan balik',
      feedbackLength: 'Panjang konten umpan balik harus 1 sampai 500 karakter',
      emailFormat: 'Mohon masukkan alamat email yang valid',
      phoneFormat: 'Mohon masukkan nomor telepon yang valid'
    }
  },
  
  vi: {
    dialogTitle: 'Phản hồi',
    feedbackTypeTitle: 'Loại Phản hồi',
    feedbackTypes: {
      feature: 'Đề xuất Tính năng',
      design: 'Thiết kế Giao diện',
      performance: 'Vấn đề Hiệu suất',
      other: 'Vấn đề Khác'
    },
    feedbackContentTitle: 'Nội dung Phản hồi',
    contactInfoTitle: 'Thông tin Liên hệ',
    feedbackPlaceholder: 'Vui lòng mô tả chi tiết đề xuất của bạn...',
    emailPlaceholder: 'Vui lòng nhập địa chỉ email',
    phonePlaceholder: 'Vui lòng nhập số điện thoại',
    cancelButton: 'Hủy',
    submitButton: 'Gửi Phản hồi',
    validation: {
      feedbackRequired: 'Vui lòng nhập nội dung phản hồi',
      feedbackLength: 'Độ dài nội dung phản hồi nên từ 1 đến 500 ký tự',
      emailFormat: 'Vui lòng nhập địa chỉ email hợp lệ',
      phoneFormat: 'Vui lòng nhập số điện thoại hợp lệ'
    }
  }
};