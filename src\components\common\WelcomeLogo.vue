<script lang="ts" setup>
import { useSliderStore } from '@/stores/slider';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const sliderStore = useSliderStore();
const {
    curFunction
} = storeToRefs(sliderStore);

</script>

<template>
    <div class="welcome-container">
        <div class="welcome-main">
            <img src="/logo.png" alt="Logo" class="welcome-logo" />
            <h2 class="welcome-title">{{ t(curFunction.subTitle) }}</h2>
            <p class="welcome-subtitle">
                {{ t(curFunction.description) }}
            </p>
        </div>
    </div>
</template>
<style scoped src="./WelcomeLogo.css"></style>
