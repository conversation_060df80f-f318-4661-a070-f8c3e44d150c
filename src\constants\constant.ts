import AiAssistantIcon from "@/components/icons/AiAssistantIcon.vue";
import DiagnosisIcon from "@/components/icons/DiagnosisIcon.vue";
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import VoiceRecordingIcon from "@/components/icons/VoiceRecordingIcon.vue";

export const HIDDEN_LAYOUT_PATH = ["/", "/privacy-policy", "/service-agreement"];

export type FunctionItem = {
  id: string;
  title: string;
  icon: any;
  subTitle: string;
  description: string;
};

export const FUNCTION_LIST: FunctionItem[] = [
  {
    id: "chat",
    title: "appSlider.functions.chat.title",
    icon: DiagnosisIcon,
    subTitle: "appSlider.functions.chat.subTitle",
    description: "appSlider.functions.chat.description",
  },
  {
    id: "knowledge",
    title: "appSlider.functions.knowledge.title",
    icon: AiAssistantIcon,
    subTitle: "appSlider.functions.knowledge.subTitle",
    description: "appSlider.functions.knowledge.description",
  },
  {
    id: "voice",
    title: "appSlider.functions.voice.title",
    icon: VoiceRecordingIcon,
    subTitle: "appSlider.functions.voice.subTitle",
    description: "appSlider.functions.voice.description",
  },
  {
    id: "image",
    title: "appSlider.functions.image.title",
    icon: ImageDiagnosisIcon,
    subTitle: "appSlider.functions.image.subTitle",
    description: "appSlider.functions.image.description",
  },
];

export const LANGUAGE_OPTIONS = [
  { label: '简体中文', value: 'zh', flag: 'cn' },  // 中国
  { label: 'English', value: 'en', flag: 'us' },   // 美国
  { label: '日本語', value: 'ja', flag: 'jp' },    // 日本
  { label: '한국어', value: 'ko', flag: 'kr' },    // 韩国
  { label: 'ไทย', value: 'th', flag: 'th' },      // 泰国
  { label: 'Bahasa Melayu', value: 'ms', flag: 'my' },  // 马来西亚
  { label: 'Bahasa Indonesia', value: 'id', flag: 'id' },  // 印度尼西亚
  { label: 'Tiếng Việt', value: 'vi', flag: 'vn' }   // 越南
];

// 会话相关常量
export const CONVERSATION_TITLE = "宠物健康助手";
export const CONVERSATION_PAGE_SIZE = 15;

// 聊天相关常量
export const CHAT_PAGE_SIZE = 10;

// 登录相关
export const AREA_CODE = [
  {
    key: "cn",
    value: "+86",
    label: "中国",
  },
  {
    key: "hk",
    value: "+852",
    label: "中国香港",
  },
  {
    key: "am",
    value: "+853",
    label: "中国澳门",
  },
  {
    key: "tw",
    value: "+886",
    label: "中国台湾",
  },
];

// 不喜欢反馈类别
export type DislikeCategory = {
  type: number;
  label: string;
};

export const DISLIKE_CATEGORIES: DislikeCategory[] = [
  { type: 1, label: "内容不准确 / 存在错误" },
  { type: 2, label: "缺少参考文献 / 引用不完整" },
  { type: 3, label: "回答太笼统 / 没有解决问题" },
  { type: 4, label: "答案不相关 / 跑题" },
  { type: 5, label: "内容过长 / 表达不清晰" },
  { type: 6, label: "存在敏感 / 不当信息" },
];

// 语音识别配置
export type SpeechRecognitionType = "aliyun" | "custom";

export const SPEECH_RECOGNITION_CONFIG = {
  // 语音识别类型：'aliyun' 使用阿里云服务，'custom' 使用自定义服务
  TYPE: "custom" as SpeechRecognitionType,

  // 阿里云语音识别服务配置
  ALIYUN: {
    // WebSocket服务地址
    WS_URL: "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1",
    // 音频格式配置
    AUDIO_FORMAT: {
      format: "pcm",
      sample_rate: 16000,
      enable_intermediate_result: true,
      enable_punctuation_prediction: true,
      enable_inverse_text_normalization: true,
    },
    // Token缓存配置
    TOKEN_CACHE: {
      CACHE_KEY: "aliyun_speech_token",
      BUFFER_TIME: 5 * 60 * 1000, // 5分钟缓冲时间
    },
    // WebSocket消息命名空间
    NAMESPACE: "SpeechTranscriber",
    // 支持的消息类型
    MESSAGE_TYPES: {
      START_TRANSCRIPTION: "StartTranscription",
      STOP_TRANSCRIPTION: "StopTranscription",
      TRANSCRIPTION_STARTED: "TranscriptionStarted",
      TRANSCRIPTION_RESULT_CHANGED: "TranscriptionResultChanged",
      TRANSCRIPTION_COMPLETED: "TranscriptionCompleted",
    },
  },

  // 自定义语音识别服务配置
  CUSTOM: {
    WS_URL: `wss://open.haoshouyi.com/ws/voice?token=sk-proj-5EqM5QObtb7gXbnSOhRTgoEjIdg6hRDNu7mWZNBdyNhUCAhN`,
    // WS_URL: `wss://************:10096`,
    // 自定义服务音频配置
    AUDIO_CONFIG: {
      chunk_size: [5, 10, 5],
      wav_name: "vue_app",
      is_speaking: true,
      chunk_interval: 10,
      itn: true, // 逆文本标准化
      mode: "2pass", // 使用2pass模式
    },
  },
};
