<script lang="ts" setup>
import CopyIcon from "@/components/icons/CopyIcon.vue";
import DislikeFilledIcon from "@/components/icons/DislikeFilledIcon.vue";
import DislikeIcon from "@/components/icons/DislikeIcon.vue";
import LikeFilledIcon from "@/components/icons/LikeFilledIcon.vue";
import LikeIcon from "@/components/icons/LikeIcon.vue";
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";
import { CHAT_PAGE_SIZE } from "@/constants/constant";
import type { BookInfo } from "@/services/knowledgeService";
import { useChatStore } from "@/stores/chat";
import { useSliderStore } from "@/stores/slider";
import Dislike from "@/views/ChatView/components/Dislike.vue";
import { ElMessage } from "element-plus";
import { throttle } from "lodash-es";
import { storeToRefs } from "pinia";
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { BubbleList } from 'vue-element-plus-x';

// 国际化
const { t } = useI18n();

// 全局的Pina库
const chatStore = useChatStore();
const sliderStore = useSliderStore();

// 全局的响应数据
const { messageList, messagePagination, messageLoading, isAiTyping } = storeToRefs(chatStore);
const { curConversation } = storeToRefs(sliderStore);

//本组件的响应数据
const showDislikeFeedbackDialog = ref<boolean>(false);
const currentDislikeMessageId = ref<number | null>(null);
const bubbleListRef = ref();
// 参考文献展开状态，key为消息ID，value为是否展开
const expandedBooks = ref<Record<number, boolean>>({});
const bubbleList = computed(() => {
  return messageList.value.map((item) => {
    return {
      ...item,
      key: item.id,
      role: item.type,
      placement: item.type === "ai" ? "start" : "end",
      content: item.content,
      loading: item.thinkLoading,
      finished: !item.loading,
      isMarkdown: item.type === "ai",
      avatar: item.type === "ai" ? "logo.png" : "",
      avatarSize: "48px",
    };
  });
});

//本组件的方法
const hasMoreMessages = computed(() => {
  const page = messagePagination.value.page;
  const pages = messagePagination.value.pages;
  return page && pages ? page < pages : true;
});
function buildCopyText(item: any): string {
  const original = item.content || "";
  // 去除文档标注的 span，只保留数字
  const stripped = original.replace(/<span[^>]*document-book[^>]*>(\d+)<\/span>/g, "$1");
  // 追加参考文献
  const books: BookInfo[] = getNoRepateBooks(item.books || []);
  if (!books || books.length === 0) return stripped;
  const refs = books
    .map((b) => `· ${b.name}${b.pages ? `（${b.pages}）` : ""}`)
    .join("\n");
  return `${stripped}\n\n${t('bubbleList.references.title')}\n${refs}`;
}

const copyMessage = async (item: any) => {
  try {
    const messageContent = buildCopyText(item);
    // 优先使用 Clipboard API（需要 HTTPS 或 localhost）
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(messageContent);
      ElMessage.success(t('bubbleList.copied'));
      return;
    }

    // 降级方案：使用传统的 document.execCommand（兼容 HTTP）
    const textArea = document.createElement("textarea");
    textArea.value = messageContent;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const successful = document.execCommand("copy");
    document.body.removeChild(textArea);

    if (successful) {
      ElMessage.success(t('bubbleList.copied'));
    } else {
      throw new Error("execCommand copy failed");
    }
  } catch (error) {
    ElMessage.error(t('bubbleList.copyFailed'));
    console.error("复制失败:", error);
  }
};
const likeMessage = async (messageId: number) => {
  await chatStore.likeMessage(messageId);
  ElMessage.success(t('bubbleList.thankYou'));
};
const unlikeMessage = async (messageId: number) => {
  await chatStore.cancelLikeMessage(messageId);
  ElMessage.success(t('bubbleList.likeRemoved'));
};
const undislikeMessage = async (messageId: number) => {
  await chatStore.cancelDislikeMessage(messageId);
  ElMessage.success(t('bubbleList.dislikeRemoved'));
};
const handleShowDislikeFeedback = (messageId: number) => {
  currentDislikeMessageId.value = messageId;
  showDislikeFeedbackDialog.value = true;
};
const handleCloseDislikeFeedback = () => {
  currentDislikeMessageId.value = null;
  showDislikeFeedbackDialog.value = false;
};
const loadMoreMessages = throttle(
  async () => {
    if (!curConversation.value?.conversation_id) return;
    if (!bubbleListRef.value) return;
    const curPage = chatStore.messagePagination.page + 1;
    await chatStore.loadMoreMessageList(
      curConversation.value.conversation_id,
      curPage
    );
    // 等待DOM更新后恢复滚动位置
    await nextTick();
    bubbleListRef.value.scrollToBubble(CHAT_PAGE_SIZE * (curPage - 1))
  },
  2000,
  {
    leading: false,
    trailing: true,
  }
);
const bubbleListScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  if (
    target?.scrollTop === 0 &&
    hasMoreMessages.value &&
    !messageLoading.value
  ) {
    loadMoreMessages();
  }
};
// 当有新消息时，自动滚动到底部
watch(
  () => isAiTyping.value,
  async (typing) => {
    if (typing) {
      await nextTick();
      bubbleListRef.value?.scrollToBottom?.();
    }
  }
);

// 会话切换时，滚动到底部
watch(
  () => curConversation.value?.conversation_id,
  async () => {
    await nextTick();
    bubbleListRef.value?.scrollToBottom?.();
  }
);

// Popover 相关状态和方法
const popoverVisible = ref(false);
const popoverContent = ref('');
const popoverTitle = ref('');
const popoverPages = ref('');
const popoverPosition = ref({ x: 0, y: 0 });
const popoverPlacement = ref<'top' | 'bottom'>('top');
let hideTimer: NodeJS.Timeout | null = null;
const getNoRepateBooks = (books: BookInfo[]) => {
  const noRepateBooks: BookInfo[] = [];
  if (books && books.length > 0) {
    const seenNames = new Set<string>();
    for (let i = 0; i < books.length; i++) {
      const book = books[i];
      if (!seenNames.has(book.name)) {
        seenNames.add(book.name);
        noRepateBooks.push(book);
      }
    }
  }
  return noRepateBooks;
};

// 切换参考文献展开状态
const toggleBooksExpansion = (messageId: number) => {
  expandedBooks.value[messageId] = !expandedBooks.value[messageId];
};

// 获取要显示的参考文献列表
const getDisplayBooks = (books: BookInfo[], messageId: number) => {
  const noRepateBooks = getNoRepateBooks(books);
  const isExpanded = expandedBooks.value[messageId];
  return isExpanded ? noRepateBooks : noRepateBooks.slice(0, 5);
};

// 检查是否需要显示展开/收起按钮
const shouldShowToggleButton = (books: BookInfo[]) => {
  const noRepateBooks = getNoRepateBooks(books);
  return noRepateBooks.length > 5;
};

// 显示Popover（带自动翻转）
const showPopover = (target: HTMLElement) => {
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }

  const content = target.getAttribute('data-content');
  const name = target.getAttribute('data-name');
  const pages = target.getAttribute('data-pages') ?? "";

  if (content && name) {
    popoverContent.value = content;
    popoverTitle.value = name;
    popoverPages.value = pages;

    // 先按默认在上方显示
    const rect = target.getBoundingClientRect();
    popoverPlacement.value = 'top';
    popoverPosition.value = {
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    };

    popoverVisible.value = true;

    // 渲染后测量高度，判断是否需要翻转到下方
    nextTick(() => {
      const el = document.querySelector('.document-popover') as HTMLElement | null;
      if (!el) return;
      const popHeight = el.offsetHeight;
      const spaceAbove = rect.top - 10; // 目标上方到视口顶部的空间
      const spaceBelow = window.innerHeight - rect.bottom - 10; // 目标下方到视口底部的空间

      if (spaceAbove < popHeight && spaceBelow >= popHeight) {
        // 上方不够且下方足够，翻到下方
        popoverPlacement.value = 'bottom';
        popoverPosition.value = {
          x: rect.left + rect.width / 2,
          y: rect.bottom + 12
        };
      } else if (spaceAbove < popHeight && spaceBelow < popHeight) {
        // 上下都不够：选择空间更大的那一侧
        if (spaceBelow > spaceAbove) {
          popoverPlacement.value = 'bottom';
          popoverPosition.value = {
            x: rect.left + rect.width / 2,
            y: rect.bottom + 12
          };
        } else {
          popoverPlacement.value = 'top';
          popoverPosition.value = {
            x: rect.left + rect.width / 2,
            y: rect.top - 10
          };
        }
      } else {
        // 上方足够，保持上方
        popoverPlacement.value = 'top';
        popoverPosition.value = {
          x: rect.left + rect.width / 2,
          y: rect.top - 10
        };
      }
    });
  }
};

// 隐藏Popover
const hidePopover = () => {
  if (hideTimer) {
    clearTimeout(hideTimer);
  }

  hideTimer = setTimeout(() => {
    popoverVisible.value = false;
    hideTimer = null;
  }, 150);
};

// 取消隐藏（当鼠标移到Popover上时）
const cancelHide = () => {
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }
};

// 滚动事件处理（只在页面滚动时隐藏Popover）
const handleScroll = (event: Event) => {
  // 只有当滚动的不是Popover内部时才隐藏
  const target = event.target as HTMLElement;
  const popoverElement = document.querySelector('.document-popover');

  // 如果是Popover内部的滚动，不隐藏
  if (popoverElement && (target === popoverElement || popoverElement.contains(target))) {
    return;
  }

  // 页面或其他容器滚动时隐藏Popover
  if (popoverVisible.value) {
    popoverVisible.value = false;
    if (hideTimer) {
      clearTimeout(hideTimer);
      hideTimer = null;
    }
  }
};

// 全局事件监听器
const handleGlobalMouseOver = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target && target.classList.contains('document-book') && target.classList.contains('circled-number')) {
    showPopover(target);
  }
};

const handleGlobalMouseOut = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target && target.classList.contains('document-book') && target.classList.contains('circled-number')) {
    hidePopover();
  }
};

// 组件挂载和卸载时的处理
onMounted(() => {
  document.addEventListener('mouseover', handleGlobalMouseOver);
  document.addEventListener('mouseout', handleGlobalMouseOut);
  // 添加滚动监听器，使用capture模式
  window.addEventListener('scroll', handleScroll, true);
});

onUnmounted(() => {
  document.removeEventListener('mouseover', handleGlobalMouseOver);
  document.removeEventListener('mouseout', handleGlobalMouseOut);
  // 移除滚动监听器
  window.removeEventListener('scroll', handleScroll, true);
  // 清理定时器
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }
});
</script>

<template>
  <div class="bubble-list-container" :class="{ 'have-more-message': hasMoreMessages }">
    <BubbleList ref="bubbleListRef" :list="bubbleList" max-height="90dvh" class="bubble-list" @scroll="bubbleListScroll"
      :backButtonPosition="{ bottom: '20dvh', left: 'calc(50% - 19px)' }">
      <template #footer="{ item }">
        <div v-if="item.books && item.books.length > 0" class="footer-book">
          <div class="footer-book-header">
            <span>{{ t('bubbleList.references.title') }}</span>
            <el-button 
              v-if="shouldShowToggleButton(item.books)"
              size="small" 
              text 
              @click="toggleBooksExpansion(item.id)"
              class="toggle-books-button"
            >
              {{ expandedBooks[item.id] ? t('bubbleList.references.collapse') : `${t('bubbleList.references.more')} (${getNoRepateBooks(item.books).length})` }}
              <el-icon v-if="expandedBooks[item.id]">
                <ArrowUpBold />
              </el-icon>
              <el-icon v-else>
                <ArrowDownBold />
              </el-icon>
            </el-button>
          </div>
          <div class="footer-book-list">
            <div class="footer-book-item" v-for="book in getDisplayBooks(item.books, item.id)" :key="book.id">
              <div class="footer-book-item-name">{{ book.name }}</div>
            </div>
          </div>
        </div>
        <div class="footer-container" v-if="item.finished">
          <div class="footer-buttons" v-if="item.role === 'ai'
            && item.content
            && typeof item.id === 'number'
            && Number.isInteger(item.id)">
            <el-button size=" small" text @click="copyMessage(item)" class="footer-button copy-button" :title="t('bubbleList.copy')">
              <CopyIcon />
            </el-button>
            <el-button v-if="!item.isLike" size="small" text @click="likeMessage(item.id)"
              class="footer-button like-button" :title="t('bubbleList.like')">
              <LikeIcon />
            </el-button>
            <el-button v-else size="small" text @click="unlikeMessage(item.id)" class="footer-button like-button liked"
              :title="t('bubbleList.unlike')">
              <LikeFilledIcon />
            </el-button>
            <el-button v-if="!item.isDislike" size="small" text @click="handleShowDislikeFeedback(item.id)"
              class="footer-button dislike-button" :title="t('bubbleList.dislike')">
              <DislikeIcon />
            </el-button>
            <el-button v-else size="small" text @click="undislikeMessage(item.id)"
              class="footer-button dislike-button disliked" :title="t('bubbleList.undislike')">
              <DislikeFilledIcon />
            </el-button>
          </div>
          <div class="footer-remarks" v-if="item.role === 'ai' && item.finished">
            {{ t('bubbleList.disclaimer.knowledge') }}
          </div>
        </div>
      </template>
    </BubbleList>

    <!-- 不喜欢反馈对话框 -->
    <Dislike v-model:visible="showDislikeFeedbackDialog" :message-id="currentDislikeMessageId"
      @close="handleCloseDislikeFeedback" />

    <!-- Popover 弹出框 -->
    <Teleport to="body">
      <div v-if="popoverVisible" class="document-popover" :class="popoverPlacement" :style="{
        left: popoverPosition.x + 'px',
        top: popoverPosition.y + 'px'
      }" @mouseenter="cancelHide" @mouseleave="hidePopover">
        <div class="popover-header">
          <strong>{{ popoverTitle }}</strong>
          <span v-if="!!popoverPages" class="popover-header-pages">（{{ popoverPages }}）</span>
        </div>
        <div class="popover-content">
          {{ popoverContent }}
        </div>
      </div>
    </Teleport>
  </div>
</template>
<style scoped src="./BubbleList.css"></style>
<style scoped src="./BubbleList.mobile.css" media="(max-width: 768px)"></style>
