﻿.feedback-dialog :deep(.el-dialog) {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(169, 169, 169, 0.05) 100%);
  backdrop-filter: blur(25px);
  border-radius: 12px;
  border: 2px solid var(--el-color-info-light-8);
  box-shadow: 0 8px 32px rgba(128, 128, 128, 0.15);
}

.feedback-dialog :deep(.el-dialog__header) {
  padding: 24px 36px 16px;
  border-bottom: 1px solid var(--el-color-info-light-9);
}

.feedback-dialog :deep(.el-dialog__title) {
  font-size: 1.1rem;
  font-weight: bold;
  color: #363636;
  letter-spacing: 0.4px;
}

.feedback-dialog :deep(.el-dialog__body) {
  padding: 24px 36px;
}

/* 表单样式 */
.feedback-form {
  display: flex;
  flex-direction: column;
  padding-inline: 6px;
}

.form-section {
  display: flex;
  flex-direction: column;
  margin-inline: 16px;
  margin-bottom: 16px;
}

.section-title {
  color: #363636;
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 16px;
  letter-spacing: 0.4px;
}

/* 反馈类型选择 */
.feedback-types {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.feedback-type-item {
  border: 2px solid var(--el-color-info-light-8);
  border-radius: 12px;
  padding-block: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.feedback-type-item:hover {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.15) 0%, rgba(169, 169, 169, 0.1) 100%);
  border-color: var(--el-color-info-light-5);
  box-shadow: 0 2px 8px rgba(128, 128, 128, 0.15);
  transform: translateY(-1px);
}

.feedback-type-item.active {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.25) 0%, rgba(169, 169, 169, 0.15) 100%);
  border-color: var(--el-color-info-light-3);
  box-shadow: 0 3px 12px rgba(128, 128, 128, 0.25);
  transform: translateY(-1px);
}

.feedback-type-item.active:hover {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.3) 0%, rgba(169, 169, 169, 0.2) 100%);
  border-color: var(--el-color-info);
  box-shadow: 0 4px 16px rgba(128, 128, 128, 0.3);
}

/* 输入框样式 */
.feedback-textarea :deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1px solid var(--el-color-info-light-9);
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  transition: all 0.3s ease;
}

.feedback-textarea :deep(.el-textarea__inner):focus {
  border-color: var(--el-color-info-light-5);
  box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.15);
}

.contact-input {
  margin-bottom: 16px;
}

.contact-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 1px solid var(--el-color-info-light-9);
  transition: all 0.3s ease;
}

.contact-input :deep(.el-input__wrapper):hover {
  border-color: var(--el-color-info-light-5);
}

.contact-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-info-light-5);
  box-shadow: 0 0 0 2px rgba(128, 128, 128, 0.15);
}

.contact-input :deep(.el-input__inner) {
  font-size: 14px;
  padding-left: 12px;
}

.contact-input :deep(.el-input__prefix) {
  color: #999;
}

/* 表单项样式 */
.feedback-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.feedback-form :deep(.el-form-item__error) {
  font-size: 0.8dvw;
  margin-top: 0.3dvw;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-inline: 36px;
  margin-top: 24px;
}

.cancel-button {
  padding-block: 24px;
  width: 30%;
  border-radius: 12px;
  font-size: 16px;
  letter-spacing: 1px;
  border: 2px solid var(--el-color-info-light-8);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(169, 169, 169, 0.05) 100%);
  color: #666;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.cancel-button:hover {
  background: linear-gradient(135deg, rgba(128, 128, 128, 0.15) 0%, rgba(169, 169, 169, 0.1) 100%);
  border-color: var(--el-color-info-light-5);
  box-shadow: 0 2px 8px rgba(128, 128, 128, 0.15);
  transform: translateY(-1px);
}

.submit-button {
  padding-block: 24px;
  width: 70%;
  border-radius: 12px;
  font-size: 16px;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  transition: left 0.5s;
}

.submit-button:hover:not(.is-loading)::before {
  left: 100%;
}

.submit-button:hover:not(.is-loading) {
  transform: translateY(-1px);
}