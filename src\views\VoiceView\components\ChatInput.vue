<script lang="ts" setup>
import StopIcon from "@/components/icons/StopIcon.vue";
import SubmitArrowIcon from "@/components/icons/SubmitArrowIcon.vue";
import { ChatService } from "@/services/chatService";
import { ElMessage } from "element-plus";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

// 国际化
const { t } = useI18n();

// 父组件的数据和方法
interface Props {
  handleSendMessage: (message?: string) => Promise<void>;
  isAiTyping: boolean;
}
const props = defineProps<Props>();

// 本组件的响应数据
const senderValue = ref("");

// 本组件的方法
const handleSendMessageLocal = async (): Promise<void> => {
  const userMessage = senderValue.value.trim() || t('chatInput.voice.generatePetSuggestions');
  if (!userMessage) {
    ElMessage.warning(t('chatInput.voice.enterMessageContent'));
    return;
  }
  if (props.isAiTyping) {
    ElMessage.warning(t('chatInput.aiReplying'));
    return;
  }

  // 清空输入框
  const messageToSend = userMessage;
  senderValue.value = "";

  // 调用父组件传递的方法
  await props.handleSendMessage(messageToSend);
};

const handleStopResponse = (): void => {
  ChatService.abortCurrentRequest();
};
</script>

<template>
  <div class="chat-input">
    <div class="chat-input-content">
      <MentionSender v-model="senderValue" @submit="handleSendMessageLocal" variant="updown"
        :auto-size="{ minRows: 2, maxRows: 6 }" class="chat-textarea" :placeholder="t('chatInput.placeholder')">
        <template #action-list>
          <div class="chat-input-main-button">
            <!-- AI正在回复时显示停止按钮 -->
            <el-button @mousedown.stop v-if="props.isAiTyping" circle @click="handleStopResponse()">
              <el-icon class="stop-icon">
                <StopIcon />
              </el-icon>
            </el-button>
            <!-- AI未回复时显示发送按钮 -->
            <el-button @mousedown.stop v-else circle @click="handleSendMessageLocal()">
              <el-icon class="submit-icon">
                <SubmitArrowIcon />
              </el-icon>
            </el-button>
          </div>
        </template>
      </MentionSender>
    </div>
    <div class="chat-input-remarks">{{ t('chatInput.aiGeneratedDisclaimer') }}</div>
  </div>
</template>
<style scoped src="./ChatInput.css"></style>
