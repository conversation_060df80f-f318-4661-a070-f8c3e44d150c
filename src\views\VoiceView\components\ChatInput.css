@import "/src/styles/components/_chat-input.css";

/* VoiceView 特有样式 */
.chat-input {
  width: 92%;
}

.uploaded-image-container {
  padding: 0.8dvw 1dvw;
}

.uploaded-header {
  font-size: 0.7dvw;
  margin-bottom: 0.2dvw;
  color: var(--el-color-info-dark-2);
}

.uploaded-image-header {
  font-size: 0.6dvw;
  margin-bottom: 0.3dvw;
  color: var(--el-color-info-light-5);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploaded-image-header::before,
.uploaded-image-header::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: var(--el-color-info-light-5);
  margin: 0 0.5dvw;
}
.uploaded-image-content {
  max-height: 8.1dvw; /* 2 rows: thumbnail height + padding + gap */
  overflow-y: auto;
  overflow-x: hidden;
}

.uploaded-image-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.4dvw 0.8dvw;
  padding-right: 0.2dvw; /* Space for scrollbar */
}

.image-item {
  display: flex;
  align-items: center;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.4) 100%
  );
  border: 2px solid rgba(0, 87, 255, 0.3);
  border-radius: 0.5dvw;
  padding: 0.3dvw;
  gap: 0.6dvw;
  min-width: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 87, 255, 0.1);
  position: relative;
}

.image-item:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.6) 100%
  );
  border-color: rgba(0, 87, 255, 0.6);
  box-shadow: 0 4px 16px rgba(0, 87, 255, 0.25);
}

.image-thumbnail {
  width: 2.5dvw;
  height: 2.5dvw;
  border-radius: 0.3dvw;
  flex-shrink: 0;
}

.image-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
  gap: 0.1dvw;
}

.image-name {
  color: rgba(51, 51, 51, 0.9);
  font-size: 0.7dvw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 8dvw;
}

.image-size {
  color: rgba(51, 51, 51, 0.6);
  font-size: 0.6dvw;
}

.image-delete-btn {
  position: absolute;
  top: 0;
  right: 0.1dvw;
  opacity: 0;
}

.image-item:hover .image-delete-btn {
  opacity: 1;
}

.image-delete-btn .el-icon {
  font-size: 0.8dvw;
}

:deep(.chat-input .el-sender-action-list) {
  display: flex;
  justify-content: end;
  width: 100%;
}

.chat-input-other-button {
  font-size: 0.8dvw;
  padding: 0.2dvw 0.8dvw;
  color: rgba(51, 51, 51, 0.85);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
  border: 1.5px solid rgba(135, 206, 235, 0.4);
  border-radius: 1dvw;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 0.01dvw;
  gap: 0.15dvw;
  backdrop-filter: blur(10px);
}

.chat-input-other-button:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  border-color: rgba(135, 206, 235, 0.6);
  box-shadow: 0 2px 6px rgba(96, 151, 252, 0.3);
}

.chat-input-other-button:active {
  transform: scale(0.95);
}

.chat-input-other-button:focus-visible {
  outline: none;
  border-color: rgba(135, 206, 235, 0.8);
  box-shadow: 0 0 0 2px rgba(135, 206, 235, 0.3);
}

.chat-bubble-container {
  margin-bottom: 0.8dvw;
  display: flex;
  flex-direction: column;
  gap: 0.5dvw;
  overflow-y: auto;
  padding: 0.5dvw;
  background: linear-gradient(
    145deg,
    rgba(135, 206, 235, 0.2) 0%,
    rgba(96, 151, 252, 0.3) 50%,
    rgba(255, 105, 230, 0.2) 100%
  );
  border-radius: 1dvw;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 12px rgba(96, 151, 252, 0.15);
}

