<script lang="ts" setup>
import ChatHeader from '@/components/common/ChatHeader.vue';
import WelcomeLogo from '@/components/common/WelcomeLogo.vue';
import { useChatStore } from '@/stores/chat';
import { useSliderStore } from '@/stores/slider';
import { storeToRefs } from 'pinia';
import BubbleList from './components/BubbleList.vue';
import ChatInput from './components/ChatInput.vue';
import { computed } from 'vue';

// 全局Pina库
const sliderStore = useSliderStore();
const chatStore = useChatStore();

// 全局响应数据
const {
  curConversation
} = storeToRefs(sliderStore);
const {
  messageList
} = storeToRefs(chatStore);

// 本组件的响应数据
const showBubbleList = computed(() => {
  return curConversation.value && messageList.value.length > 0
})

</script>

<template>
  <ChatHeader />
  <div class="page-container">
    <div class="page-left">
      <BubbleList v-if="showBubbleList" />
      <WelcomeLogo v-else />
      <ChatInput :class="{ 'fly-chat-input': showBubbleList }"/>
    </div>
    <div class="page-right">
    </div>
  </div>
</template>
<style scoped src="./index.css"></style>
