<script lang="ts" setup>
import AlgorithmIcon from "@/components/icons/AlgorithmIcon.vue";
import CustomerServiceIcon from "@/components/icons/CustomerServiceIcon.vue";
import DatabaseIcon from "@/components/icons/DatabaseIcon.vue";
import EmailIcon from "@/components/icons/EmailIcon.vue";
import PhoneIcon from "@/components/icons/PhoneIcon.vue";
import SecurityIcon from "@/components/icons/SecurityIcon.vue";
import StarIcon from "@/components/icons/StarIcon.vue";
import { useRouter } from "vue-router";
import { ref, onMounted, onUnmounted } from "vue";

const router = useRouter();

// 滚动动画相关
const homeTwoRef = ref<HTMLElement>();
const homeTwoBoxRefs = ref<HTMLElement[]>([]);
const isHomeTwoVisible = ref(false);
const visibleBoxes = ref<boolean[]>([false, false, false, false]);

const handleGoChatView = () => {
  router.push("/chat");
};

const handleOpenIcp = () => {
  window.location.href = "https://beian.miit.gov.cn"
};

const handleOpenPrivacyPolicy = () => {
  window.open("/privacy-policy", "_blank");
};

const handleOpenServiceAgreement = () => {
  window.open("/service-agreement", "_blank");
};

type ShowProduct = {
  title: string;
  desc: string;
  buttons: string[];
  img: string;
};

const showProductList: ShowProduct[] = [
  {
    title: "AI 辅助诊断",
    desc:
      "运用先进的深度学习算法，结合海量临床数据，为兽医提供准确的诊断建议和治疗方案。系统可快速分析患病动物的症状特征，提供相似病例参考，帮助医生做出更生的诊断决策。",
    buttons: ["智能诊断", "病例分析", "治疗建议"],
    img: "/test.jpeg",
  },
  {
    title: "智能知识助手",
    desc:
      "基于强大的知识图谱，为兽医提供专业的医学知识查询和临床指南参考。覆盖疾病诊断、用药指导、手术方案等多个领域，让专业知识触手可及。",
    buttons: ["知识检索", "临床指南", "用药参考"],
    img: "/test.jpeg",
  },
  {
    title: "智能语音病历",
    desc:
      "通过先进的语音识别技术，将医生的口述自动转换为规范的电子病历。支持实时记录、智能纠错和病历模板定制，大幅提升工作效率。",
    buttons: ["语音录入", "自动转写", "模板定制"],
    img: "/test.jpeg",
  },
];

type UserRating = {
  doctorName: string;
  company: string;
  content: string;
  avatar: string;
};

type UserRatingSlide = {
  left: UserRating;
  right: UserRating;
};

const userRatingList: UserRatingSlide[] = [
  {
    left: {
      doctorName: "刘医生",
      company: "北京宠爱动物医院",
      content: "好兽医 AI 助手太靠谱了！之前狗狗总挠耳朵，描述后立刻分析可能是耳螨，详细讲了清洁步骤，还提醒若有红肿就得就医。按建议处理后真的好转，比自己乱查资料省心多了，省去好多没必要的担心，养宠有它太安心！",
      avatar: "/test2.png",
    },
    right: {
      doctorName: "赵医生",
      company: "上海瑞鹏宠物医院",
      content: "宠物有点打喷嚏流鼻水，描述后助手很快判断可能是着凉，给了保暖和补充营养的建议，特别提醒发烧就得就医。跟着做症状减轻了，不用动不动跑医院，方便又靠谱，养宠家庭必备！",
      avatar: "/test2.png",
    },
  },
  {
    left: {
      doctorName: "王医生",
      company: "深圳爱康宠物诊所",
      content: "猫咪最近食欲不振，通过AI助手描述症状后，很快得到专业分析，可能是肠胃问题。助手详细指导了饮食调整和观察要点，还提醒如果持续不进食要及时就医。按建议调理后猫咪恢复食欲，真的很实用！",
      avatar: "/test2.png",
    },
    right: {
      doctorName: "李医生",
      company: "广州博爱动物医院",
      content: "狗狗突然拉肚子，急得不行，用AI助手咨询后立即得到专业建议。助手分析了可能原因，给出了饮食控制和观察重点，特别强调脱水症状要及时送医。按照指导处理，狗狗很快康复，省心又专业！",
      avatar: "/test2.png",
    },
  },
  {
    left: {
      doctorName: "陈医生",
      company: "杭州宠福宠物医院",
      content: "兔子突然不吃草了，很担心，通过AI助手咨询后得到专业指导。助手分析了可能原因，建议观察粪便和活动情况，还提醒如果持续不进食要检查牙齿。按建议观察后发现问题并及时处理，真的很感谢！",
      avatar: "/test2.png",
    },
    right: {
      doctorName: "张医生",
      company: "成都瑞派宠物医院",
      content: "仓鼠最近总是打喷嚏，用AI助手咨询后得到详细分析。助手判断可能是垫料过敏，建议更换垫料并保持环境清洁，还提醒如果症状加重要及时就医。按建议处理后症状明显改善，太实用了！",
      avatar: "/test2.png",
    },
  },
];

// 滚动监听函数
const handleScroll = () => {
  if (!homeTwoRef.value) return;

  const rect = homeTwoRef.value.getBoundingClientRect();
  const windowHeight = window.innerHeight;

  // 当home-two区域进入视口时触发动画
  if (rect.top < windowHeight * 0.8 && rect.bottom > 0) {
    if (!isHomeTwoVisible.value) {
      isHomeTwoVisible.value = true;
      // 逐个显示box，每个延迟200ms
      visibleBoxes.value.forEach((_, index) => {
        setTimeout(() => {
          visibleBoxes.value[index] = true;
        }, index * 200);
      });
    }
  }
};

// 生成粒子样式
const getParticleStyle = () => {
  const size = Math.random() * 4 + 2;
  const left = Math.random() * 100;
  const animationDelay = Math.random() * 20;
  const animationDuration = Math.random() * 10 + 15;

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`,
  };
};



// 组件挂载和卸载时的处理
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  handleScroll(); // 初始检查
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<template>
  <div class="home-container">
    <div class="home-one">
      <img class="home-one-bg bg-opacity-in" src="/home/<USER>" />
      <div class="home-one-particles">
        <div class="particle" v-for="n in 50" :key="n" :style="getParticleStyle()"></div>
      </div>
      <div class="home-one-text">
        <div class="home-one-text-title opacity-in">好兽医 AI助手</div>
        <div class="home-one-text-subtitle slide-in-right">赋能动物医疗新未来</div>
        <div class="home-one-text-desc fade-in-up">
          <p class="home-one-text-desc-p slide-in-left delay-1">好兽医AI助手是一款为动物医院、宠物诊所以及兽医专业人士量身打造的智能助手。</p>
          <p class="home-one-text-desc-p slide-in-left delay-2">借助最新的宠物医疗算法和语音识别技术，将繁琐的病历录入、信息整理、病情分析</p>
          <p class="home-one-text-desc-p slide-in-left delay-3">与客户沟通流程自动化，让兽医们能够更多地专注于诊疗本身。</p>
          <button class="home-one-text-button pulse-button scale-in" @click="handleGoChatView">
            <span class="button-text">立即体验网页版</span>
            <div class="button-glow"></div>
          </button>
        </div>
      </div>
    </div>

    <div class="home-two" ref="homeTwoRef">
      <!-- 科技感背景动画 -->
      <div class="home-two-particles">
        <!-- 动态几何图形 -->
        <div class="tech-geometry">
          <div class="tech-circle tech-circle-1"></div>
          <div class="tech-circle tech-circle-2"></div>
          <div class="tech-hexagon tech-hexagon-1"></div>
          <div class="tech-hexagon tech-hexagon-2"></div>
          <div class="tech-line tech-line-1"></div>
          <div class="tech-line tech-line-2"></div>
          <div class="tech-line tech-line-3"></div>
          <!-- 数据流光点 -->
          <div class="data-stream">
            <div class="data-dot data-dot-1"></div>
            <div class="data-dot data-dot-2"></div>
            <div class="data-dot data-dot-3"></div>
            <div class="data-dot data-dot-4"></div>
          </div>
        </div>
      </div>

      <div class="home-two-text" :class="{ 'animate-title': isHomeTwoVisible }">
        <div class="home-two-text-title">为什么选择我们</div>
        <div class="home-two-text-desc">我们致力于为兽医行业提供最专业、最智能的 AI 助手</div>
      </div>
      <div class="home-two-list">
        <div
          class="home-two-box"
          :class="{ 'animate-box': visibleBoxes[0] }"
          ref="homeTwoBoxRefs"
        >
          <div class="home-two-box-icon">
            <AlgorithmIcon class="home-two-box-icon-svg floating-icon" />
          </div>
          <div class="home-two-box-title">
            先进算法
          </div>
          <div class="home-two-box-desc">
            采用最新深度学习技术，诊断准确率达 90% 以上
          </div>
        </div>
        <div
          class="home-two-box"
          :class="{ 'animate-box': visibleBoxes[1] }"
          ref="homeTwoBoxRefs"
        >
          <div class="home-two-box-icon">
            <DatabaseIcon class="home-two-box-icon-svg floating-icon" />
          </div>
          <div class="home-two-box-title">
            海量数据
          </div>
          <div class="home-two-box-desc">
            超千万临床病例数据支持，覆盖上百种疾病类型
          </div>
        </div>
        <div
          class="home-two-box"
          :class="{ 'animate-box': visibleBoxes[2] }"
          ref="homeTwoBoxRefs"
        >
          <div class="home-two-box-icon">
            <SecurityIcon class="home-two-box-icon-svg floating-icon" />
          </div>
          <div class="home-two-box-title">
            安全可靠
          </div>
          <div class="home-two-box-desc">
            通过 ISO27001 认证，确保数据安全，保护隐私
          </div>
        </div>
        <div
          class="home-two-box"
          :class="{ 'animate-box': visibleBoxes[3] }"
          ref="homeTwoBoxRefs"
        >
          <div class="home-two-box-icon">
            <CustomerServiceIcon class="home-two-box-icon-svg floating-icon" />
          </div>
          <div class="home-two-box-title">
            专业服务
          </div>
          <div class="home-two-box-desc">
            7×24 小时技术支持，确保系统稳定运行
          </div>
        </div>
      </div>
    </div>

    <div class="home-three">
      <div class="home-three-text-title">产品功能展示</div>
      <div class="home-three-carousel">
        <el-carousel height="50dvh" motion-blur>
          <el-carousel-item v-for="(s, i) in showProductList" :key="i">
            <div class="home-three-carousel-item">
              <div class="home-three-carousel-item-left">
                <div class="home-three-carousel-item-title">{{ s.title }}</div>
                <div class="home-three-carousel-item-desc">{{ s.desc }}</div>
                <div class="home-three-carousel-item-button-list">
                  <div class="home-three-carousel-item-button" v-for="(b, bi) in s.buttons" :key="bi">{{ b }}</div>
                </div>
              </div>
              <div class="home-three-carousel-item-right">
                <img :src="s.img" class="home-three-carousel-item-img" />
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>

    <div class="home-four">
      <div class="home-four-text-title">用户反馈</div>
      <div class="home-four-carousel">
        <el-carousel height="50dvh" motion-blur>
          <el-carousel-item v-for="(slide, index) in userRatingList" :key="index">
            <div class="home-four-carousel-item">
              <div class="home-four-carousel-item-left">
                <div class="home-four-carousel-item-doctor">
                  <img :src="slide.left.avatar" class="home-four-carousel-item-doctor-img" />
                  <div class="home-four-carousel-item-doctor-info">
                    <div class="home-four-carousel-item-doctor-name">{{ slide.left.doctorName }}</div>
                    <div class="home-four-carousel-item-doctor-company">{{ slide.left.company }}</div>
                  </div>
                </div>
                <div class="home-four-carousel-item-desc">
                  {{ slide.left.content }}
                </div>
                <div class="home-four-carousel-item-star">
                  <StarIcon v-for="i in 5" :key="i" style="color: #ffe500; font-size: 1dvw;" />
                </div>
              </div>
              <div class="home-four-carousel-item-right">
                <div class="home-four-carousel-item-doctor">
                  <img :src="slide.right.avatar" class="home-four-carousel-item-doctor-img" />
                  <div class="home-four-carousel-item-doctor-info">
                    <div class="home-four-carousel-item-doctor-name">{{ slide.right.doctorName }}</div>
                    <div class="home-four-carousel-item-doctor-company">{{ slide.right.company }}</div>
                  </div>
                </div>
                <div class="home-four-carousel-item-desc">
                  {{ slide.right.content }}
                </div>
                <div class="home-four-carousel-item-star">
                  <StarIcon v-for="i in 5" :key="i" style="color: #ffe500; font-size: 1dvw;" />
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <div class="home-footer">
      <div class="home-footer-item">
        <h3>关于我们</h3>
        <div class="home-footer-desc">好兽医AI助手致力于为动物医疗行业提供智能化医疗解决方案，让宠物医疗更加便捷高效。</div>
      </div>
      <div class="home-footer-item">
        <h3>联系方式</h3>
        <div class="home-footer-desc contact-information">
          <EmailIcon class="home-footer-desc-icon" />
          <span><EMAIL></span>
        </div>
        <div class="home-footer-desc contact-information">
          <PhoneIcon class="home-footer-desc-icon" />
          <span>************</span>
        </div>
      </div>
      <div class="home-footer-item">
        <h3>快速链接</h3>
        <div class="home-footer-desc home-text-pointer" @click="handleOpenPrivacyPolicy">隐私政策</div>
        <div class="home-footer-desc home-text-pointer" @click="handleOpenServiceAgreement">服务条款</div>
      </div>
      <div class="home-footer-item">
        <h3>关注我们</h3>
        <div class="home-footer-item-qrcode-list">
          <div class="home-footer-item-qrcode">
            <el-popover placement="top" width=300>
              <template #reference>
                <img class="home-footer-item-image" src="/home/<USER>" />
              </template>
              <template #default>
                <img class="home-footer-item-image-popover" src="/home/<USER>" />
              </template>
            </el-popover>
            <div class="home-footer-item-title">服务号</div>
          </div>
          <div class="home-footer-item-qrcode">
            <el-popover placement="top" width=300>
              <template #reference>
                <img class="home-footer-item-image" src="/home/<USER>" />
              </template>
              <template #default>
                <img class="home-footer-item-image-popover" src="/home/<USER>" />
              </template>
            </el-popover>
            <div class="home-footer-item-title">视频号</div>
          </div>
        </div>
      </div>
    </div>
    <div class="home-registration">
      <div class="home-registration-text">
        <span class="home-text-pointer" @click="handleOpenIcp">
          粤ICP备2024314826号-6
        </span>
      </div>
      <div class="home-registration-text">©2025 好兽医AI助手</div>
      <div class="home-registration-text">广东省深圳市福田区沙头街道天安社区泰然七路1号博今商务广场A座二十二层2205-5</div>
    </div>
  </div>
</template>
<style scoped src="./HomeView.css"></style>