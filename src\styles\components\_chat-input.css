:deep(.chat-textarea .el-sender-content.content-variant-updown) {
  padding-block: var(--el-padding-sm, 16px);
}

:deep(.chat-textarea .el-sender) {
  border-radius: 24px;
}

.chat-input {
  margin-inline: auto;
  position: sticky;
  bottom: 0;
}

.chat-input.fly-chat-input {
  position: absolute;
  bottom: 0;
  background: #f8f9fa;
}

.chat-input-content {
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 24px;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.6) 100%
  );
  backdrop-filter: blur(15px);
  box-shadow: 0 2px 12px rgba(96, 151, 252, 0.2);
}

/* Element UI 深度样式 */
:deep(.chat-input .el-sender) {
  border-radius: 0.8dvw;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(20px);
}

:deep(.chat-input .el-sender-content) {
  border-top: none;
  padding: 0.8dvw 1dvw;
}

:deep(.chat-input .el-textarea__inner) {
  color: #333 !important;
  background: transparent;
}

:deep(.chat-input .el-input__inner) {
  color: #333 !important;
}

:deep(.chat-input .el-sender-footer) {
  border-top: none;
  padding: 0 1dvw 0.8dvw;
  display: flex;
  gap: 0.6dvw;
}

.chat-input-remarks {
  font-size: 0.7dvw;
  text-align: center;
  color: rgba(51, 51, 51, 0.6);
  margin-block: 0.5dvw;
}

/* 发送按钮样式 */
:deep(.chat-input-main-button .el-button.is-circle) {
  background: linear-gradient(135deg, #87ceeb 0%, #6097fc 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

:deep(.chat-input-main-button .el-button.is-circle:hover) {
  background: linear-gradient(135deg, #a0d8f0 0%, #80b3ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(135, 206, 235, 0.4);
}

:deep(.chat-input-main-button .el-button.is-circle:active) {
  transform: translateY(0) scale(0.95);
  box-shadow: 0 2px 8px rgba(135, 206, 235, 0.3);
}

:deep(.chat-input-main-button .el-button.is-circle:disabled) {
  background: linear-gradient(
    135deg,
    rgba(135, 206, 235, 0.3) 0%,
    rgba(96, 151, 252, 0.3) 100%
  );
  color: rgba(255, 255, 255, 0.5);
  transform: none;
  box-shadow: none;
}
