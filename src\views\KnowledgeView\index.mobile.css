/* KnowledgeView 移动端样式适配 */

.page-container {
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: stretch !important;
  height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden;
}

.page-left {
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: stretch !important;
  justify-content: flex-start !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden;
}

.page-right {
  display: none !important; /* 移动端隐藏右侧区域 */
}

/* 移动端聊天输入框定位 */
.fly-chat-input {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding: 12px 8px !important;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 移动端欢迎页面样式 */
:deep(.welcome-container) {
  padding: 20px 16px !important;
  height: calc(100vh - 120px) !important; /* 为底部输入框留出空间 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.welcome-main) {
  text-align: center !important;
  max-width: 100% !important;
  padding: 0 16px !important;
}

:deep(.welcome-logo) {
  width: 80px !important;
  height: 80px !important;
  margin-bottom: 20px !important;
}

:deep(.welcome-title) {
  font-size: 20px !important;
  margin-bottom: 12px !important;
  line-height: 1.3 !important;
  color: var(--el-text-color-primary) !important;
}

:deep(.welcome-subtitle) {
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: var(--el-text-color-regular) !important;
  margin-bottom: 0 !important;
}

/* 移动端气泡列表容器调整 */
.bubble-list-container {
  flex: 1 !important;
  overflow: hidden !important;
  padding-bottom: 80px !important; /* 为固定的输入框留出空间 */
}

/* 移动端安全区域适配 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .fly-chat-input {
    padding-bottom: calc(12px + env(safe-area-inset-bottom)) !important;
  }
  
  .bubble-list-container {
    padding-bottom: calc(80px + env(safe-area-inset-bottom)) !important;
  }
}

/* 移动端键盘弹出适配 */
@media screen and (max-height: 600px) {
  .bubble-list-container {
    padding-bottom: 70px !important;
  }
  
  .fly-chat-input {
    padding: 8px 6px !important;
  }
  
  :deep(.welcome-container) {
    height: calc(100vh - 100px) !important;
    padding: 16px 12px !important;
  }
  
  :deep(.welcome-logo) {
    width: 60px !important;
    height: 60px !important;
    margin-bottom: 16px !important;
  }
  
  :deep(.welcome-title) {
    font-size: 18px !important;
    margin-bottom: 10px !important;
  }
  
  :deep(.welcome-subtitle) {
    font-size: 13px !important;
  }
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .page-container {
    height: 100vh !important;
  }
  
  .bubble-list-container {
    padding-bottom: 60px !important;
  }
  
  .fly-chat-input {
    padding: 6px 8px !important;
  }
  
  :deep(.welcome-container) {
    height: calc(100vh - 80px) !important;
    padding: 12px 16px !important;
  }
  
  :deep(.welcome-logo) {
    width: 50px !important;
    height: 50px !important;
    margin-bottom: 12px !important;
  }
  
  :deep(.welcome-title) {
    font-size: 16px !important;
    margin-bottom: 8px !important;
  }
  
  :deep(.welcome-subtitle) {
    font-size: 12px !important;
  }
}

/* 小屏幕设备特殊适配 */
@media (max-width: 480px) {
  .fly-chat-input {
    padding: 10px 6px !important;
  }
  
  :deep(.welcome-container) {
    padding: 16px 12px !important;
  }
  
  :deep(.welcome-main) {
    padding: 0 12px !important;
  }
}

/* 超小屏幕设备适配 */
@media (max-width: 360px) {
  .fly-chat-input {
    padding: 8px 4px !important;
  }
  
  :deep(.welcome-container) {
    padding: 12px 8px !important;
  }
  
  :deep(.welcome-main) {
    padding: 0 8px !important;
  }
  
  :deep(.welcome-logo) {
    width: 70px !important;
    height: 70px !important;
  }
  
  :deep(.welcome-title) {
    font-size: 18px !important;
  }
  
  :deep(.welcome-subtitle) {
    font-size: 13px !important;
  }
}
