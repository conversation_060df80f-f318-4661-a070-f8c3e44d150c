export const systemMessages = {
  zh: {
    // 基础操作
    operationSuccess: '操作成功',
    requestFailed: '请求失败',
    networkError: '网络错误',
    requestTimeout: '请求超时，请稍后重试',
    loadingTooFrequent: '加载太频繁，请稍候...',
    
    // 登录相关
    loginExpired: '登录已过期，请重新登录',
    notLoggedIn: '您还未登录，请先登录',
    logoutConfirm: '确定要退出登录吗？',
    logoutTitle: '退出确认',
    confirm: '确定',
    cancel: '取消',
    logoutSuccess: '退出登录成功',
    
    // 聊天相关
    enterMessageContent: '请输入消息内容',
    aiReplying: 'AI正在回复中，请稍候...',
    cannotReplyNow: '抱歉，我现在无法回复您的消息，请稍后再试。',
    generatePetSuggestions: '根据宠物病历生成宠物建议',
    
    // 语音识别相关
    switchedToCustomService: '已切换到自定义语音识别服务',
    usingAliyunService: '当前使用阿里云语音识别服务',
    needMicrophonePermission: '需要麦克风权限才能录音',
    initializingSpeechRecognition: '正在初始化语音识别...',
    startRecordingSuccess: '开始录音和语音识别',
    recordingEnded: '录音结束',
    allowMicrophonePermission: '请允许使用麦克风权限',
    microphoneNotFound: '未找到麦克风设备',
    getMicrophonePermissionFailed: '获取麦克风权限失败',
    getAudioDevicesFailed: '获取音频设备失败',
    stopRecordingBeforeSwitch: '请停止录音后再切换设备',
    switchedToDevice: '已切换到：{label}',
    speechRecognitionError: '语音识别错误：',
    startRecordingFailed: '开始录音失败：',
    stopRecordingFailed: '停止录音失败：',
    switchAudioDeviceFailed: '切换音频设备失败：'
  },
  en: {
    // 基础操作
    operationSuccess: 'Operation successful',
    requestFailed: 'Request failed',
    networkError: 'Network error',
    requestTimeout: 'Request timeout, please try again later',
    loadingTooFrequent: 'Loading too frequently, please wait...',
    
    // 登录相关
    loginExpired: 'Login expired, please log in again',
    notLoggedIn: 'You are not logged in, please log in first',
    logoutConfirm: 'Are you sure you want to log out?',
    logoutTitle: 'Logout Confirmation',
    confirm: 'Confirm',
    cancel: 'Cancel',
    logoutSuccess: 'Logout successful',
    
    // 聊天相关
    enterMessageContent: 'Please enter message content',
    aiReplying: 'AI is replying, please wait...',
    cannotReplyNow: 'Sorry, I cannot reply to your message right now, please try again later.',
    generatePetSuggestions: 'Generate pet suggestions based on medical records',
    
    // 语音识别相关
    switchedToCustomService: 'Switched to custom speech recognition service',
    usingAliyunService: 'Currently using Alibaba Cloud speech recognition service',
    needMicrophonePermission: 'Microphone permission is required for recording',
    initializingSpeechRecognition: 'Initializing speech recognition...',
    startRecordingSuccess: 'Started recording and speech recognition',
    recordingEnded: 'Recording ended',
    allowMicrophonePermission: 'Please allow microphone permission',
    microphoneNotFound: 'Microphone device not found',
    getMicrophonePermissionFailed: 'Failed to get microphone permission',
    getAudioDevicesFailed: 'Failed to get audio devices',
    stopRecordingBeforeSwitch: 'Please stop recording before switching devices',
    switchedToDevice: 'Switched to: {label}',
    speechRecognitionError: 'Speech recognition error: ',
    startRecordingFailed: 'Failed to start recording: ',
    stopRecordingFailed: 'Failed to stop recording: ',
    switchAudioDeviceFailed: 'Failed to switch audio device: '
  },
  ja: {
    // 基础操作
    operationSuccess: '操作成功',
    requestFailed: 'リクエスト失敗',
    networkError: 'ネットワークエラー',
    requestTimeout: 'リクエストタイムアウト、後でもう一度お試しください',
    loadingTooFrequent: '読み込みが頻繁すぎます、しばらくお待ちください...',
    
    // 登录相关
    loginExpired: 'ログインが期限切れです、再びログインしてください',
    notLoggedIn: 'ログインしていません、まずログインしてください',
    logoutConfirm: 'ログアウトしてもよろしいですか?',
    logoutTitle: 'ログアウト確認',
    confirm: '確認',
    cancel: 'キャンセル',
    logoutSuccess: 'ログアウト成功',
    
    // 聊天相关
    enterMessageContent: 'メッセージ内容を入力してください',
    aiReplying: 'AIが返信中です、しばらくお待ちください...',
    cannotReplyNow: '申し訳ありませんが、今はお返事できません。後でもう一度お試しください。',
    generatePetSuggestions: '医療記録に基づいてペットの提案を生成',
    
    // 语音识别相关
    switchedToCustomService: 'カスタム音声認識サービスに切り替えました',
    usingAliyunService: '現在Alibaba Cloud音声認識サービスを使用中',
    needMicrophonePermission: '録音にはマイクの許可が必要です',
    initializingSpeechRecognition: '音声認識を初期化中...',
    startRecordingSuccess: '録音と音声認識を開始しました',
    recordingEnded: '録音終了',
    allowMicrophonePermission: 'マイクの許可を許可してください',
    microphoneNotFound: 'マイクデバイスが見つかりません',
    getMicrophonePermissionFailed: 'マイクの許可取得に失敗しました',
    getAudioDevicesFailed: 'オーディオデバイスの取得に失敗しました',
    stopRecordingBeforeSwitch: 'デバイスを切り替える前に録音を停止してください',
    switchedToDevice: '切り替え先：{label}',
    speechRecognitionError: '音声認識エラー：'
  },
  ko: {
    // 基础操作
    operationSuccess: '작업 성공',
    requestFailed: '요청 실패',
    networkError: '네트워크 오류',
    requestTimeout: '요청 시간 초과, 나중에 다시 시도해주세요',
    loadingTooFrequent: '로딩이 너무 빈번합니다. 잠시 기다려주세요...',
    
    // 登录相关
    loginExpired: '로그인이 만료되었습니다. 다시 로그인해주세요',
    notLoggedIn: '로그인하지 않았습니다. 먼저 로그인해주세요',
    logoutConfirm: '로그아웃하시겠습니까?',
    logoutTitle: '로그아웃 확인',
    confirm: '확인',
    cancel: '취소',
    logoutSuccess: '로그아웃 성공',
    
    // 聊天相关
    enterMessageContent: '메시지 내용을 입력해주세요',
    aiReplying: 'AI가 응답 중입니다. 잠시 기다려주세요...',
    cannotReplyNow: '죄송합니다. 지금은 메시지에 응답할 수 없습니다. 나중에 다시 시도해주세요.',
    generatePetSuggestions: '의료 기록을 바탕으로 반려동물 제안 생성',
    
    // 语音识别相关
    switchedToCustomService: '사용자 정의 음성 인식 서비스로 전환되었습니다',
    usingAliyunService: '현재 Alibaba Cloud 음성 인식 서비스를 사용 중입니다',
    needMicrophonePermission: '녹음에는 마이크 권한이 필요합니다',
    initializingSpeechRecognition: '음성 인식을 초기화하는 중...',
    startRecordingSuccess: '녹음 및 음성 인식이 시작되었습니다',
    recordingEnded: '녹음 종료',
    allowMicrophonePermission: '마이크 권한을 허용해주세요',
    microphoneNotFound: '마이크 장치를 찾을 수 없습니다',
    getMicrophonePermissionFailed: '마이크 권한 획득에 실패했습니다',
    getAudioDevicesFailed: '오디오 장치 획득에 실패했습니다',
    stopRecordingBeforeSwitch: '장치를 전환하기 전에 녹음을 중지해주세요',
    switchedToDevice: '전환됨: {label}',
    speechRecognitionError: '음성 인식 오류: '
  },
  th: {
    // 基础操作
    operationSuccess: 'ดำเนินการสำเร็จ',
    requestFailed: 'คำขอล้มเหลว',
    networkError: 'ข้อผิดพลาดเครือข่าย',
    requestTimeout: 'คำขอหมดเวลา กรุณาลองใหม่อีกครั้งในภายหลัง',
    loadingTooFrequent: 'โหลดบ่อยเกินไป กรุณารอสักครู่...',
    
    // 登录相关
    loginExpired: 'การเข้าสู่ระบบหมดอายุแล้ว กรุณาเข้าสู่ระบบใหม่',
    notLoggedIn: 'คุณยังไม่ได้เข้าสู่ระบบ กรุณาเข้าสู่ระบบก่อน',
    logoutConfirm: 'คุณแน่ใจว่าต้องการออกจากระบบหรือไม่?',
    logoutTitle: 'ยืนยันการออกจากระบบ',
    confirm: 'ยืนยัน',
    cancel: 'ยกเลิก',
    logoutSuccess: 'ออกจากระบบสำเร็จ',
    
    // 聊天相关
    enterMessageContent: 'กรุณาป้อนเนื้อหาข้อความ',
    aiReplying: 'AI กำลังตอบกลับ กรุณารอสักครู่...',
    cannotReplyNow: 'ขออภัย ตอนนี้ไม่สามารถตอบข้อความของคุณได้ กรุณาลองใหม่อีกครั้งในภายหลัง',
    generatePetSuggestions: 'สร้างข้อเสนอแนะสัตว์เลี้ยงตามบันทึกการรักษา',
    
    // 语音识别相关
    switchedToCustomService: 'ได้เปลี่ยนไปใช้บริการรู้จำเสียงที่กำหนดเอง',
    usingAliyunService: 'ปัจจุบันใช้บริการรู้จำเสียง Alibaba Cloud',
    needMicrophonePermission: 'จำเป็นต้องได้รับอนุญาตไมโครโฟนสำหรับการบันทึก',
    initializingSpeechRecognition: 'กำลังเริ่มต้นการรู้จำเสียง...',
    startRecordingSuccess: 'เริ่มการบันทึกและการรู้จำเสียงแล้ว',
    recordingEnded: 'การบันทึกสิ้นสุดลง',
    allowMicrophonePermission: 'กรุณาอนุญาตการใช้ไมโครโฟน',
    microphoneNotFound: 'ไม่พบอุปกรณ์ไมโครโฟน',
    getMicrophonePermissionFailed: 'ไม่สามารถขอสิทธิ์ไมโครโฟนได้',
    getAudioDevicesFailed: 'ไม่สามารถขอรับอุปกรณ์เสียงได้',
    stopRecordingBeforeSwitch: 'กรุณาหยุดการบันทึกก่อนเปลี่ยนอุปกรณ์',
    switchedToDevice: 'เปลี่ยนไปยัง: {label}',
    speechRecognitionError: 'ข้อผิดพลาดการรู้จำเสียง: '
  },
  ms: {
    // 基础操作
    operationSuccess: 'Operasi berjaya',
    requestFailed: 'Permintaan gagal',
    networkError: 'Ralat rangkaian',
    requestTimeout: 'Permintaan tamat masa, sila cuba lagi kemudian',
    loadingTooFrequent: 'Memuatkan terlalu kerap, sila tunggu...',
    
    // 登录相关
    loginExpired: 'Log masuk telah tamat tempoh, sila log masuk semula',
    notLoggedIn: 'Anda belum log masuk, sila log masuk dahulu',
    logoutConfirm: 'Adakah anda pasti mahu log keluar?',
    logoutTitle: 'Pengesahan Log Keluar',
    confirm: 'Sahkan',
    cancel: 'Batal',
    logoutSuccess: 'Log keluar berjaya',
    
    // 聊天相关
    enterMessageContent: 'Sila masukkan kandungan mesej',
    aiReplying: 'AI sedang membalas, sila tunggu...',
    cannotReplyNow: 'Maaf, saya tidak dapat membalas mesej anda sekarang, sila cuba lagi kemudian.',
    generatePetSuggestions: 'Jana cadangan haiwan berdasarkan rekod perubatan',
    
    // 语音识别相关
    switchedToCustomService: 'Beralih kepada perkhidmatan pengecaman suara khas',
    usingAliyunService: 'Sedang menggunakan perkhidmatan pengecaman suara Alibaba Cloud',
    needMicrophonePermission: 'Kebenaran mikrofon diperlukan untuk rakaman',
    initializingSpeechRecognition: 'Memulakan pengecaman suara...',
    startRecordingSuccess: 'Mula merakam dan pengecaman suara',
    recordingEnded: 'Rakaman tamat',
    allowMicrophonePermission: 'Sila benarkan kebenaran mikrofon',
    microphoneNotFound: 'Peranti mikrofon tidak dijumpai',
    getMicrophonePermissionFailed: 'Gagal mendapatkan kebenaran mikrofon',
    getAudioDevicesFailed: 'Gagal mendapatkan peranti audio',
    stopRecordingBeforeSwitch: 'Sila hentikan rakaman sebelum menukar peranti',
    switchedToDevice: 'Beralih kepada: {label}',
    speechRecognitionError: 'Ralat pengecaman suara: '
  },
  id: {
    // 基础操作
    operationSuccess: 'Operasi berhasil',
    requestFailed: 'Permintaan gagal',
    networkError: 'Kesalahan jaringan',
    requestTimeout: 'Permintaan timeout, silakan coba lagi nanti',
    loadingTooFrequent: 'Memuat terlalu sering, silakan tunggu...',
    
    // 登录相关
    loginExpired: 'Login telah kedaluwarsa, silakan login kembali',
    notLoggedIn: 'Anda belum login, silakan login terlebih dahulu',
    logoutConfirm: 'Apakah Anda yakin ingin logout?',
    logoutTitle: 'Konfirmasi Logout',
    confirm: 'Konfirmasi',
    cancel: 'Batal',
    logoutSuccess: 'Logout berhasil',
    
    // 聊天相关
    enterMessageContent: 'Silakan masukkan konten pesan',
    aiReplying: 'AI sedang membalas, silakan tunggu...',
    cannotReplyNow: 'Maaf, saya tidak dapat membalas pesan Anda sekarang, silakan coba lagi nanti.',
    generatePetSuggestions: 'Buat saran hewan peliharaan berdasarkan catatan medis',
    
    // 语音识别相关
    switchedToCustomService: 'Beralih ke layanan pengenalan suara kustom',
    usingAliyunService: 'Saat ini menggunakan layanan pengenalan suara Alibaba Cloud',
    needMicrophonePermission: 'Izin mikrofon diperlukan untuk perekaman',
    initializingSpeechRecognition: 'Menginisialisasi pengenalan suara...',
    startRecordingSuccess: 'Mulai merekam dan pengenalan suara',
    recordingEnded: 'Perekaman berakhir',
    allowMicrophonePermission: 'Silakan izinkan akses mikrofon',
    microphoneNotFound: 'Perangkat mikrofon tidak ditemukan',
    getMicrophonePermissionFailed: 'Gagal mendapatkan izin mikrofon',
    getAudioDevicesFailed: 'Gagal mendapatkan perangkat audio',
    stopRecordingBeforeSwitch: 'Silakan hentikan perekaman sebelum beralih perangkat',
    switchedToDevice: 'Beralih ke: {label}',
    speechRecognitionError: 'Kesalahan pengenalan suara: '
  },
  vi: {
    // 基础操作
    operationSuccess: 'Thao tác thành công',
    requestFailed: 'Yêu cầu thất bại',
    networkError: 'Lỗi mạng',
    requestTimeout: 'Yêu cầu hết thời gian, vui lòng thử lại sau',
    loadingTooFrequent: 'Tải quá thường xuyên, vui lòng đợi...',
    
    // 登录相关
    loginExpired: 'Đăng nhập đã hết hạn, vui lòng đăng nhập lại',
    notLoggedIn: 'Bạn chưa đăng nhập, vui lòng đăng nhập trước',
    logoutConfirm: 'Bạn có chắc chắn muốn đăng xuất?',
    logoutTitle: 'Xác nhận đăng xuất',
    confirm: 'Xác nhận',
    cancel: 'Hủy',
    logoutSuccess: 'Đăng xuất thành công',
    
    // 聊天相关
    enterMessageContent: 'Vui lòng nhập nội dung tin nhắn',
    aiReplying: 'AI đang trả lời, vui lòng đợi...',
    cannotReplyNow: 'Xin lỗi, tôi không thể trả lời tin nhắn của bạn ngay bây giờ, vui lòng thử lại sau.',
    generatePetSuggestions: 'Tạo đề xuất thú cưng dựa trên hồ sơ y tế',
    
    // 语音识别相关
    switchedToCustomService: 'Đã chuyển sang dịch vụ nhận dạng giọng nói tùy chỉnh',
    usingAliyunService: 'Hiện đang sử dụng dịch vụ nhận dạng giọng nói Alibaba Cloud',
    needMicrophonePermission: 'Cần quyền truy cập microphone để ghi âm',
    initializingSpeechRecognition: 'Đang khởi tạo nhận dạng giọng nói...',
    startRecordingSuccess: 'Bắt đầu ghi âm và nhận dạng giọng nói',
    recordingEnded: 'Ghi âm kết thúc',
    allowMicrophonePermission: 'Vui lòng cho phép quyền truy cập microphone',
    microphoneNotFound: 'Không tìm thấy thiết bị microphone',
    getMicrophonePermissionFailed: 'Không thể lấy quyền truy cập microphone',
    getAudioDevicesFailed: 'Không thể lấy thiết bị âm thanh',
    stopRecordingBeforeSwitch: 'Vui lòng dừng ghi âm trước khi chuyển thiết bị',
    switchedToDevice: 'Đã chuyển sang: {label}',
    speechRecognitionError: 'Lỗi nhận dạng giọng nói: '
  }
};

export default systemMessages;