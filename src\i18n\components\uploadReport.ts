export const uploadReport = {
  zh: {
    title: '影像报告上传',
    upload: {
      loading: '正在上传 {count} 个文件，请稍候...',
      dragText: '拖动影像报告文件到此处 或',
      clickText: '点击选择文件',
      pasteText: 'Ctrl+V 粘贴图片',
      pasteFileName: '粘贴影像报告_{timestamp}.png',
      processingError: '处理粘贴文件时出错:'
    },
    buttons: {
      preview: '预览',
      delete: '删除',
      cancel: '取消',
      complete: '完成 ({count})'
    }
  },
  en: {
    title: 'Medical Report Upload',
    upload: {
      loading: 'Uploading {count} files, please wait...',
      dragText: 'Drag medical report files here or',
      clickText: 'click to select files',
      pasteText: 'Ctrl+V to paste images',
      pasteFileName: 'Pasted_Report_{timestamp}.png',
      processingError: 'Error processing pasted file:'
    },
    buttons: {
      preview: 'Preview',
      delete: 'Delete',
      cancel: 'Cancel',
      complete: 'Complete ({count})'
    }
  },
  ja: {
    title: '医療レポートのアップロード',
    upload: {
      loading: '{count}個のファイルをアップロード中です。お待ちください...',
      dragText: '医療レポートファイルをここにドラッグするか',
      clickText: 'クリックしてファイルを選択',
      pasteText: 'Ctrl+Vで画像を貼り付け',
      pasteFileName: 'ペーストレポート_{timestamp}.png',
      processingError: 'ペーストファイルの処理中にエラーが発生しました:'
    },
    buttons: {
      preview: 'プレビュー',
      delete: '削除',
      cancel: 'キャンセル',
      complete: '完了 ({count})'
    }
  },
  ko: {
    title: '의료 보고서 업로드',
    upload: {
      loading: '{count}개의 파일을 업로드 중입니다. 잠시 기다려 주세요...',
      dragText: '의료 보고서 파일을 여기로 드래그하거나',
      clickText: '클릭하여 파일 선택',
      pasteText: 'Ctrl+V로 이미지 붙여넣기',
      pasteFileName: '붙여넣기_보고서_{timestamp}.png',
      processingError: '붙여넣기 파일 처리 중 오류 발생:'
    },
    buttons: {
      preview: '미리보기',
      delete: '삭제',
      cancel: '취소',
      complete: '완료 ({count})'
    }
  },
  th: {
    title: 'อัปโหลดรายงานทางการแพทย์',
    upload: {
      loading: 'กำลังอัปโหลด {count} ไฟล์ กรุณารอสักครู่...',
      dragText: 'ลากไฟล์รายงานทางการแพทย์มาที่นี่ หรือ',
      clickText: 'คลิกเพื่อเลือกไฟล์',
      pasteText: 'Ctrl+V เพื่อวางภาพ',
      pasteFileName: 'รายงานที่วาง_{timestamp}.png',
      processingError: 'เกิดข้อผิดพลาดในการประมวลผลไฟล์ที่วาง:'
    },
    buttons: {
      preview: 'ดูตัวอย่าง',
      delete: 'ลบ',
      cancel: 'ยกเลิก',
      complete: 'เสร็จสิ้น ({count})'
    }
  },
  ms: {
    title: 'Muat Naik Laporan Perubatan',
    upload: {
      loading: 'Memuat naik {count} fail, sila tunggu...',
      dragText: 'Seret fail laporan perubatan ke sini atau',
      clickText: 'klik untuk memilih fail',
      pasteText: 'Ctrl+V untuk tampal imej',
      pasteFileName: 'Laporan_Tampal_{timestamp}.png',
      processingError: 'Ralat semasa memproses fail yang ditampal:'
    },
    buttons: {
      preview: 'Pratonton',
      delete: 'Padam',
      cancel: 'Batal',
      complete: 'Selesai ({count})'
    }
  },
  id: {
    title: 'Unggah Laporan Medis',
    upload: {
      loading: 'Mengunggah {count} file, mohon tunggu...',
      dragText: 'Seret file laporan medis ke sini atau',
      clickText: 'klik untuk memilih file',
      pasteText: 'Ctrl+V untuk menempel gambar',
      pasteFileName: 'Laporan_Tempel_{timestamp}.png',
      processingError: 'Kesalahan saat memproses file yang ditempel:'
    },
    buttons: {
      preview: 'Pratinjau',
      delete: 'Hapus',
      cancel: 'Batal',
      complete: 'Selesai ({count})'
    }
  },
  vi: {
    title: 'Tải lên Báo cáo Y tế',
    upload: {
      loading: 'Đang tải lên {count} tệp, vui lòng đợi...',
      dragText: 'Kéo tệp báo cáo y tế vào đây hoặc',
      clickText: 'nhấn để chọn tệp',
      pasteText: 'Ctrl+V để dán hình ảnh',
      pasteFileName: 'Báo_cáo_Dán_{timestamp}.png',
      processingError: 'Lỗi khi xử lý tệp đã dán:'
    },
    buttons: {
      preview: 'Xem trước',
      delete: 'Xóa',
      cancel: 'Hủy',
      complete: 'Hoàn thành ({count})'
    }
  }
};