/* BubbleList 移动端样式适配 */

/* 移动端容器布局调整 */
.bubble-list-container .el-bubble-list.bubble-list {
  padding-inline: 8px 8px !important; /* 移动端减少左右内边距 */
  padding-top: 16px;
  padding-bottom: 16px;
}

/* 移动端气泡样式调整 */
:deep(.bubble-list .el-bubble.el-bubble-start .el-bubble-content.el-bubble-content-filled) {
  width: 100% !important;
  max-width: 100% !important;
  margin-right: 0;
  border-radius: 16px 16px 16px 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.bubble-list .el-bubble.el-bubble-end .el-bubble-content.el-bubble-content-filled) {
  border-radius: 16px 16px 4px 16px;
  box-shadow: 0 2px 8px rgba(0, 87, 255, 0.15);
}

/* 移动端头像调整 */
:deep(.bubble-list .el-bubble-avatar) {
  width: 36px !important;
  height: 36px !important;
  margin-right: 8px;
}

:deep(.bubble-list .el-bubble-avatar img) {
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

/* 移动端文本样式 */
:deep(.bubble-list .el-bubble-content) {
  font-size: 15px !important;
  line-height: 1.5;
  padding: 12px 16px;
}

/* 移动端参考文献样式 */
.footer-book {
  margin-top: 12px;
  padding: 12px;
  background: rgba(0, 87, 255, 0.05);
  border-radius: 12px;
  border-left: 3px solid var(--el-color-primary);
}

.footer-book-header {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
}

.toggle-books-button {
  font-size: 12px !important;
  padding: 4px 8px !important;
  min-height: 28px !important;
  border-radius: 6px;
}

.footer-book-item {
  margin-block: 6px !important;
  padding: 4px 0;
}

.footer-book-item-name {
  font-size: 13px;
  line-height: 1.4;
  word-break: break-word;
}

/* 移动端文档标注样式 */
:deep(.bubble-list .document-book.circled-number) {
  width: 20px !important;
  height: 20px !important;
  font-size: 11px !important;
  margin-inline: 3px !important;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

:deep(.bubble-list .document-book.circled-number:active) {
  transform: scale(0.9);
  background: var(--el-color-primary-light-6);
}

/* 移动端 Popover 样式调整 */
.document-popover {
  max-width: 280px !important;
  min-width: 240px !important;
  font-size: 13px !important;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
}

.popover-header {
  padding: 10px 14px;
  font-size: 12px !important;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #0057ff 0%, #4a9eff 100%);
}

.popover-content {
  padding: 12px 14px;
  font-size: 12px !important;
  line-height: 1.4;
  max-height: 150px !important;
}

/* 移动端操作按钮样式 */
.footer-container {
  margin-top: 8px;
}

.footer-buttons {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

:deep(.footer-button) {
  min-width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease;
}

:deep(.footer-button:active) {
  transform: scale(0.9);
}

:deep(.footer-button svg) {
  width: 16px;
  height: 16px;
}

/* 移动端免责声明 */
.footer-remarks {
  font-size: 11px !important;
  line-height: 1.3;
  margin-top: 8px;
  padding: 6px 8px;
  background: rgba(255, 179, 0, 0.1);
  border-radius: 6px;
  border-left: 2px solid rgb(255, 179, 0);
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .bubble-list-container .el-bubble-list.bubble-list {
    padding-inline: 12px 12px !important;
  }
  
  :deep(.bubble-list .el-bubble-avatar) {
    width: 32px !important;
    height: 32px !important;
  }
  
  :deep(.bubble-list .el-bubble-content) {
    font-size: 14px !important;
    padding: 10px 14px;
  }
  
  .document-popover {
    max-width: 240px !important;
    min-width: 200px !important;
  }
}

/* 小屏幕设备特殊适配 */
@media (max-width: 480px) {
  .bubble-list-container .el-bubble-list.bubble-list {
    padding-inline: 6px 6px !important;
  }
  
  :deep(.bubble-list .el-bubble-content) {
    font-size: 14px !important;
    padding: 10px 12px;
  }
  
  .footer-book {
    padding: 10px;
    margin-top: 10px;
  }
  
  .footer-book-header {
    font-size: 13px;
  }
  
  .footer-book-item-name {
    font-size: 12px;
  }
}

/* 超小屏幕设备适配 */
@media (max-width: 360px) {
  :deep(.bubble-list .el-bubble-content) {
    font-size: 13px !important;
    padding: 8px 10px;
  }
  
  :deep(.bubble-list .el-bubble-avatar) {
    width: 30px !important;
    height: 30px !important;
    margin-right: 6px;
  }
  
  .document-popover {
    max-width: 260px !important;
    min-width: 220px !important;
  }
  
  .footer-remarks {
    font-size: 10px !important;
  }
}
