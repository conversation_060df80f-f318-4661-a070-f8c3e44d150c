// VoiceInput 组件的国际化内容
export const voiceInput = {
  zh: {
    // 录音按钮
    startRecording: '开始录音',
    stopRecording: '停止录音',
    
    // 语音提示
    recordingInProgress: '正在录音中...',
    recordingTip: '开始录音前请确认已获得宠主同意',
    
    // 麦克风设置
    microphoneSettings: '麦克风设置',
    settingsTitle: '麦克风设置',
    
    // 权限状态
    permissionStatus: '权限状态：',
    permissionGranted: '已授权',
    permissionDenied: '未授权',
    getPermission: '获取权限',
    
    // 设备选择
    selectMicrophone: '选择麦克风：',
    selectMicrophonePlaceholder: '请选择麦克风设备',
    devicesFound: '共找到 {count} 个麦克风设备',
    noDevicesFound: '未找到可用的麦克风设备',
    
    // 提示信息
    permissionTip: '请先授权使用麦克风，然后可以选择设备',
    refreshDevices: '刷新设备'
  },
  
  en: {
    // 录音按钮
    startRecording: 'Start Recording',
    stopRecording: 'Stop Recording',
    
    // 语音提示
    recordingInProgress: 'Recording in progress...',
    recordingTip: 'Please confirm pet owner consent before recording',
    
    // 麦克风设置
    microphoneSettings: 'Microphone Settings',
    settingsTitle: 'Microphone Settings',
    
    // 权限状态
    permissionStatus: 'Permission Status:',
    permissionGranted: 'Granted',
    permissionDenied: 'Denied',
    getPermission: 'Get Permission',
    
    // 设备选择
    selectMicrophone: 'Select Microphone:',
    selectMicrophonePlaceholder: 'Please select microphone device',
    devicesFound: '{count} microphone devices found',
    noDevicesFound: 'No available microphone devices found',
    
    // 提示信息
    permissionTip: 'Please authorize microphone access first, then select device',
    refreshDevices: 'Refresh Devices'
  },
  
  ja: {
    // 录音按钮
    startRecording: '録音開始',
    stopRecording: '録音停止',
    
    // 语音提示
    recordingInProgress: '録音中...',
    recordingTip: '録音前にペットオーナーの同意を確認してください',
    
    // 麦克风设置
    microphoneSettings: 'マイク設定',
    settingsTitle: 'マイク設定',
    
    // 权限状态
    permissionStatus: '権限状態：',
    permissionGranted: '許可済み',
    permissionDenied: '未許可',
    getPermission: '権限取得',
    
    // 设备选择
    selectMicrophone: 'マイク選択：',
    selectMicrophonePlaceholder: 'マイクデバイスを選択してください',
    devicesFound: 'マイクデバイスが{count}個見つかりました',
    noDevicesFound: '利用可能なマイクデバイスが見つかりません',
    
    // 提示信息
    permissionTip: '最初にマイクの使用許可を取得してから、デバイスを選択してください',
    refreshDevices: 'デバイス更新'
  },
  
  ko: {
    // 录音按钮
    startRecording: '녹음 시작',
    stopRecording: '녹음 중지',
    
    // 语音提示
    recordingInProgress: '녹음 중...',
    recordingTip: '녹음 전에 반려동물 주인의 동의를 확인해 주세요',
    
    // 麦克风设置
    microphoneSettings: '마이크 설정',
    settingsTitle: '마이크 설정',
    
    // 权限状态
    permissionStatus: '권한 상태:',
    permissionGranted: '허용됨',
    permissionDenied: '거부됨',
    getPermission: '권한 받기',
    
    // 设备选择
    selectMicrophone: '마이크 선택:',
    selectMicrophonePlaceholder: '마이크 장치를 선택해 주세요',
    devicesFound: '마이크 장치 {count}개를 찾았습니다',
    noDevicesFound: '사용 가능한 마이크 장치를 찾을 수 없습니다',
    
    // 提示信息
    permissionTip: '먼저 마이크 사용을 허용한 후 장치를 선택할 수 있습니다',
    refreshDevices: '장치 새로고침'
  },
  
  th: {
    // 录音按钮
    startRecording: 'เริ่มบันทึกเสียง',
    stopRecording: 'หยุดบันทึกเสียง',
    
    // 语音提示
    recordingInProgress: 'กำลังบันทึกเสียง...',
    recordingTip: 'กรุณายืนยันความยินยอมจากเจ้าของสัตว์เลี้ยงก่อนบันทึกเสียง',
    
    // 麦克风设置
    microphoneSettings: 'การตั้งค่าไมโครโฟน',
    settingsTitle: 'การตั้งค่าไมโครโฟน',
    
    // 权限状态
    permissionStatus: 'สถานะสิทธิ์:',
    permissionGranted: 'ได้รับอนุญาต',
    permissionDenied: 'ไม่ได้รับอนุญาต',
    getPermission: 'ขอสิทธิ์',
    
    // 设备选择
    selectMicrophone: 'เลือกไมโครโฟน:',
    selectMicrophonePlaceholder: 'กรุณาเลือกอุปกรณ์ไมโครโฟน',
    devicesFound: 'พบอุปกรณ์ไมโครโฟน {count} เครื่อง',
    noDevicesFound: 'ไม่พบอุปกรณ์ไมโครโฟนที่ใช้งานได้',
    
    // 提示信息
    permissionTip: 'กรุณาอนุญาตการใช้ไมโครโฟนก่อน จากนั้นจึงสามารถเลือกอุปกรณ์ได้',
    refreshDevices: 'รีเฟรชอุปกรณ์'
  },
  
  ms: {
    // 录音按钮
    startRecording: 'Mula Rakam',
    stopRecording: 'Berhenti Rakam',
    
    // 语音提示
    recordingInProgress: 'Sedang merakam...',
    recordingTip: 'Sila sahkan persetujuan pemilik haiwan sebelum merakam',
    
    // 麦克风设置
    microphoneSettings: 'Tetapan Mikrofon',
    settingsTitle: 'Tetapan Mikrofon',
    
    // 权限状态
    permissionStatus: 'Status Kebenaran:',
    permissionGranted: 'Dibenarkan',
    permissionDenied: 'Dinafikan',
    getPermission: 'Dapatkan Kebenaran',
    
    // 设备选择
    selectMicrophone: 'Pilih Mikrofon:',
    selectMicrophonePlaceholder: 'Sila pilih peranti mikrofon',
    devicesFound: '{count} peranti mikrofon dijumpai',
    noDevicesFound: 'Tiada peranti mikrofon yang tersedia dijumpai',
    
    // 提示信息
    permissionTip: 'Sila benarkan akses mikrofon terlebih dahulu, kemudian pilih peranti',
    refreshDevices: 'Segar Semula Peranti'
  },
  
  id: {
    // 录音按钮
    startRecording: 'Mulai Rekam',
    stopRecording: 'Berhenti Rekam',
    
    // 语音提示
    recordingInProgress: 'Sedang merekam...',
    recordingTip: 'Mohon konfirmasi persetujuan pemilik hewan sebelum merekam',
    
    // 麦克风设置
    microphoneSettings: 'Pengaturan Mikrofon',
    settingsTitle: 'Pengaturan Mikrofon',
    
    // 权限状态
    permissionStatus: 'Status Izin:',
    permissionGranted: 'Diizinkan',
    permissionDenied: 'Ditolak',
    getPermission: 'Dapatkan Izin',
    
    // 设备选择
    selectMicrophone: 'Pilih Mikrofon:',
    selectMicrophonePlaceholder: 'Mohon pilih perangkat mikrofon',
    devicesFound: '{count} perangkat mikrofon ditemukan',
    noDevicesFound: 'Tidak ada perangkat mikrofon yang tersedia',
    
    // 提示信息
    permissionTip: 'Mohon izinkan akses mikrofon terlebih dahulu, kemudian pilih perangkat',
    refreshDevices: 'Segarkan Perangkat'
  },
  
  vi: {
    // 录音按钮
    startRecording: 'Bắt đầu Ghi âm',
    stopRecording: 'Dừng Ghi âm',
    
    // 语音提示
    recordingInProgress: 'Đang ghi âm...',
    recordingTip: 'Vui lòng xác nhận sự đồng ý của chủ thú cưng trước khi ghi âm',
    
    // 麦克风设置
    microphoneSettings: 'Cài đặt Micro',
    settingsTitle: 'Cài đặt Micro',
    
    // 权限状态
    permissionStatus: 'Trạng thái Quyền:',
    permissionGranted: 'Đã cấp',
    permissionDenied: 'Bị từ chối',
    getPermission: 'Xin Quyền',
    
    // 设备选择
    selectMicrophone: 'Chọn Micro:',
    selectMicrophonePlaceholder: 'Vui lòng chọn thiết bị micro',
    devicesFound: 'Tìm thấy {count} thiết bị micro',
    noDevicesFound: 'Không tìm thấy thiết bị micro khả dụng',
    
    // 提示信息
    permissionTip: 'Vui lòng cho phép truy cập micro trước, sau đó chọn thiết bị',
    refreshDevices: 'Làm mới Thiết bị'
  }
};