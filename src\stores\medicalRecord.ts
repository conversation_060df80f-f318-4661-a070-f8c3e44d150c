import { VoiceService, type VoiceMedicalRecord } from "@/services/voiceService";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useMedicalRecordStore = defineStore("medicalRecord", () => {
    const medicalRecord = ref<VoiceMedicalRecord>();

    // 生成语音病历
    const createVoiceMedicalRecord = async (voiceText: string, audioFile?: Blob): Promise<VoiceMedicalRecord> => {
        return await VoiceService.createVoiceMedicalRecordFormApi(voiceText, audioFile);
    };

    // 获取语音病历
    const getVoiceMedicalRecord = async (id: number): Promise<VoiceMedicalRecord> => {
        return await VoiceService.getVoiceMedicalRecordFormApi(id);
    };

    // 修改语音病历
    const updateVoiceMedicalRecord = async (medicalRecord: VoiceMedicalRecord): Promise<VoiceMedicalRecord> => {
        return await VoiceService.updateVoiceMedicalRecordFormApi(medicalRecord);
    };

    return {
        medicalRecord,
        createVoiceMedicalRecord,
        updateVoiceMedicalRecord,
        getVoiceMedicalRecord
    };
});
