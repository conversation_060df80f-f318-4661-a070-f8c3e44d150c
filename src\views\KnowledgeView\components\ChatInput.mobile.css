/* ChatInput 移动端样式适配 */

.chat-input {
  width: 95% !important; /* 移动端占用更多宽度 */
  margin: 0 auto;
  padding: 0 10px;
}

.chat-input-content {
  margin-bottom: 8px;
}

/* 移动端文本域样式调整 */
:deep(.chat-input .el-sender) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.chat-input .el-sender .el-textarea__inner) {
  font-size: 16px !important; /* 防止iOS缩放 */
  line-height: 1.4;
  padding: 12px 16px;
  border-radius: 12px;
  min-height: 44px; /* 确保触摸友好的最小高度 */
}

/* 移动端按钮样式调整 */
:deep(.chat-input .el-sender-action-list) {
  padding: 8px 12px;
  gap: 8px;
}

.chat-input-main-button {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.chat-input-main-button .el-button) {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 87, 255, 0.3);
  transition: all 0.2s ease;
}

:deep(.chat-input-main-button .el-button:active) {
  transform: scale(0.95);
  box-shadow: 0 1px 3px rgba(0, 87, 255, 0.4);
}

/* 图标大小调整 */
:deep(.chat-input-main-button .el-icon) {
  font-size: 18px;
}

.stop-icon,
.submit-icon {
  width: 18px;
  height: 18px;
}

/* 移动端免责声明样式 */
.chat-input-remarks {
  font-size: 11px !important;
  line-height: 1.3;
  text-align: center;
  padding: 0 16px;
  color: var(--el-text-color-secondary);
  margin-top: 6px;
}

/* 移动端键盘弹出时的适配 */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari 特定样式 */
  :deep(.chat-input .el-textarea__inner) {
    transform: translateZ(0); /* 硬件加速，减少重绘 */
  }
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .chat-input {
    width: 90% !important;
  }
  
  :deep(.chat-input .el-textarea__inner) {
    min-height: 36px;
    padding: 8px 12px;
  }
  
  :deep(.chat-input-main-button .el-button) {
    width: 36px !important;
    height: 36px !important;
  }
  
  :deep(.chat-input-main-button .el-icon) {
    font-size: 16px;
  }
  
  .chat-input-remarks {
    font-size: 10px !important;
    margin-top: 4px;
  }
}

/* 小屏幕设备特殊适配 */
@media (max-width: 480px) {
  .chat-input {
    width: 98% !important;
    padding: 0 5px;
  }
  
  :deep(.chat-input .el-textarea__inner) {
    font-size: 15px !important;
    padding: 10px 14px;
  }
  
  .chat-input-remarks {
    font-size: 10px !important;
    padding: 0 12px;
  }
}

/* 超小屏幕设备适配 */
@media (max-width: 360px) {
  :deep(.chat-input .el-textarea__inner) {
    font-size: 14px !important;
    padding: 8px 12px;
  }
  
  :deep(.chat-input-main-button .el-button) {
    width: 36px !important;
    height: 36px !important;
  }
  
  :deep(.chat-input-main-button .el-icon) {
    font-size: 16px;
  }
}
