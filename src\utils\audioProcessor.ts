/**
 * 音频处理工具类
 * 处理录音数据的转换和格式化
 */

export interface AudioProcessorConfig {
  sampleRate: number;
  channels: number;
  bitDepth: number;
}

export interface AudioVolumeCallback {
  (volume: number): void;
}

class AudioProcessor {
  private audioContext: AudioContext | null = null;
  private scriptProcessor: ScriptProcessorNode | null = null;
  private mediaStreamSource: MediaStreamAudioSourceNode | null = null;
  private config: AudioProcessorConfig;
  private volumeCallback: AudioVolumeCallback | null = null;
  private analyserNode: AnalyserNode | null = null;
  private volumeDataArray: Uint8Array | null = null;

  constructor(config: AudioProcessorConfig = {
    sampleRate: 16000,
    channels: 1,
    bitDepth: 16
  }) {
    this.config = config;
  }

  /**
   * 初始化音频上下文
   */
  async initializeAudioContext(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate
      });

      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      console.log('[AudioProcessor] 音频上下文初始化成功，采样率:', this.audioContext.sampleRate);
    } catch (error) {
      throw new Error('音频上下文初始化失败: ' + error);
    }
  }

  /**
   * 开始处理音频流
   * @param stream 媒体流
   * @param onAudioData 音频数据回调
   * @param onVolumeChange 音量变化回调
   */
  startProcessing(
    stream: MediaStream, 
    onAudioData: (buffer: ArrayBuffer) => void,
    onVolumeChange?: AudioVolumeCallback
  ): void {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    this.volumeCallback = onVolumeChange || null;

    try {
      // 创建媒体流音频源
      this.mediaStreamSource = this.audioContext.createMediaStreamSource(stream);

      // 创建分析器节点用于音量检测
      this.analyserNode = this.audioContext.createAnalyser();
      this.analyserNode.fftSize = 256;
      this.analyserNode.smoothingTimeConstant = 0.8;
      
      const bufferLength = this.analyserNode.frequencyBinCount;
      this.volumeDataArray = new Uint8Array(bufferLength);

      // 创建脚本处理器节点
      const bufferSize = 4096;
      this.scriptProcessor = this.audioContext.createScriptProcessor(bufferSize, 1, 1);

      this.scriptProcessor.onaudioprocess = (event) => {
        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        
        // 转换为PCM格式
        const pcmBuffer = this.convertToPCM(inputData);
        onAudioData(pcmBuffer);
        
        // 检测音量
        this.detectVolume();
      };

      // 连接音频节点
      this.mediaStreamSource.connect(this.analyserNode);
      this.analyserNode.connect(this.scriptProcessor);
      this.scriptProcessor.connect(this.audioContext.destination);

      console.log('[AudioProcessor] 音频处理开始');
    } catch (error) {
      throw new Error('音频处理启动失败: ' + error);
    }
  }

  /**
   * 停止音频处理
   */
  stopProcessing(): void {
    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect();
      this.scriptProcessor = null;
    }

    if (this.analyserNode) {
      this.analyserNode.disconnect();
      this.analyserNode = null;
    }

    if (this.mediaStreamSource) {
      this.mediaStreamSource.disconnect();
      this.mediaStreamSource = null;
    }

    this.volumeCallback = null;
    this.volumeDataArray = null;

    console.log('[AudioProcessor] 音频处理已停止');
  }

  /**
   * 检测音频音量
   */
  private detectVolume(): void {
    if (!this.analyserNode || !this.volumeDataArray || !this.volumeCallback) {
      return;
    }

    // 获取频域数据
    this.analyserNode.getByteFrequencyData(this.volumeDataArray);
    
    // 计算平均音量
    let sum = 0;
    for (let i = 0; i < this.volumeDataArray.length; i++) {
      sum += this.volumeDataArray[i];
    }
    
    const average = sum / this.volumeDataArray.length;
    // 将音量范围从 0-255 转换为 0-1
    const normalizedVolume = Math.min(1, average / 255);
    
    // 应用平滑处理，让音量变化更自然
    const smoothedVolume = Math.pow(normalizedVolume, 0.5); // 使用平方根让低音量更敏感
    
    this.volumeCallback(smoothedVolume);
  }

  /**
   * 转换音频数据为PCM格式
   * @param float32Array 浮点数组音频数据
   * @returns PCM格式的ArrayBuffer
   */
  private convertToPCM(float32Array: Float32Array): ArrayBuffer {
    const buffer = new ArrayBuffer(float32Array.length * 2);
    const view = new DataView(buffer);
    let offset = 0;

    for (let i = 0; i < float32Array.length; i++, offset += 2) {
      const s = Math.max(-1, Math.min(1, float32Array[i]));
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
    }

    return buffer;
  }

  /**
   * 重新采样音频数据
   * @param audioBuffer 音频缓冲区
   * @param targetSampleRate 目标采样率
   */
  resampleAudioBuffer(audioBuffer: AudioBuffer, targetSampleRate: number): AudioBuffer {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化');
    }

    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      audioBuffer.duration * targetSampleRate,
      targetSampleRate
    );

    const bufferSource = offlineContext.createBufferSource();
    bufferSource.buffer = audioBuffer;
    bufferSource.connect(offlineContext.destination);
    bufferSource.start(0);

    return offlineContext.startRendering() as any;
  }

  /**
   * 获取当前音频音量（0-1范围）
   */
  getCurrentVolume(): number {
    if (!this.analyserNode || !this.volumeDataArray) {
      return 0;
    }

    this.analyserNode.getByteFrequencyData(this.volumeDataArray);
    
    let sum = 0;
    for (let i = 0; i < this.volumeDataArray.length; i++) {
      sum += this.volumeDataArray[i];
    }
    
    const average = sum / this.volumeDataArray.length;
    const normalizedVolume = Math.min(1, average / 255);
    
    return Math.pow(normalizedVolume, 0.5);
  }

  /**
   * 获取音频上下文
    return this.audioContext;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.stopProcessing();
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    console.log('[AudioProcessor] 资源已清理');
  }

  /**
   * 检测浏览器音频支持
   */
  static checkBrowserSupport(): { supported: boolean; message: string } {
    if (!window.AudioContext && !(window as any).webkitAudioContext) {
      return {
        supported: false,
        message: '浏览器不支持Web Audio API'
      };
    }

    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      return {
        supported: false,
        message: '浏览器不支持获取用户媒体设备'
      };
    }

    if (!window.WebSocket) {
      return {
        supported: false,
        message: '浏览器不支持WebSocket'
      };
    }

    return {
      supported: true,
      message: '浏览器支持所需的所有功能'
    };
  }
}

export default AudioProcessor;