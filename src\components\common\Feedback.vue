<script setup lang="ts">
import { LoginService, type FeedbackInfo } from "@/services/loginService";
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";

// 国际化
const { t } = useI18n();

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 反馈类型选项
const feedbackTypes = computed(() => [
  { label: t('feedback.feedbackTypes.feature'), value: 1 },
  { label: t('feedback.feedbackTypes.design'), value: 2 },
  { label: t('feedback.feedbackTypes.performance'), value: 3 },
  { label: t('feedback.feedbackTypes.other'), value: 4 },
]);

// 表单数据
const formData = reactive<FeedbackInfo>({
  feedback_type: 1,
  feedback_content: "",
  contact_email: "",
  contact_phone: "",
});

// 表单引用和状态
const formRef = ref();
const loading = ref(false);

// 表单验证规则
const rules = computed(() => ({
  feedback_content: [
    { required: true, message: t('feedback.validation.feedbackRequired'), trigger: "blur" },
    {
      min: 1,
      max: 500,
      message: t('feedback.validation.feedbackLength'),
      trigger: "blur",
    },
  ],
  contact_email: [
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: t('feedback.validation.emailFormat'),
      trigger: "blur",
    },
  ],
  contact_phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: t('feedback.validation.phoneFormat'),
      trigger: "blur",
    },
  ],
}));

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
const buttonDisabled = computed(
  () =>
    !formData.feedback_content ||
    !formData.feedback_type
);

// 选择反馈类型
const selectFeedbackType = (type: number) => {
  if (formData.feedback_type === type) {
    formData.feedback_type = undefined;
  } else {
    formData.feedback_type = type;
  }
};

// 重置表单
const resetForm = () => {
  formData.feedback_type = 1;
  formData.feedback_content = "";
  formData.contact_email = undefined;
  formData.contact_phone = undefined;
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  resetForm();
  dialogVisible.value = false;
};

// 提交反馈
const submitFeedback = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();
    loading.value = true;
    // 调用API提交反馈
    await LoginService.createFeedbackFromApi(formData);
    handleClose();
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('feedback.dialogTitle')"
    width="35dvw"
    :before-close="handleClose"
    class="feedback-dialog"
    center
    top="8dvh"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      class="feedback-form"
    >
      <!-- 反馈类型 -->
      <div class="form-section">
        <h3 class="section-title">{{ t('feedback.feedbackTypeTitle') }}</h3>
        <div class="feedback-types">
          <div
            v-for="type in feedbackTypes"
            :key="type.value"
            :class="[
              'feedback-type-item',
              { active: formData.feedback_type === type.value },
            ]"
            @click="selectFeedbackType(type.value)"
          >
            {{ type.label }}
          </div>
        </div>
      </div>

      <!-- 反馈内容 -->
      <div class="form-section">
        <h3 class="section-title">{{ t('feedback.feedbackContentTitle') }}</h3>
        <el-form-item prop="feedback_content">
          <el-input
            v-model="formData.feedback_content"
            type="textarea"
            :placeholder="t('feedback.feedbackPlaceholder')"
            :rows="6"
            maxlength="500"
            show-word-limit
            class="feedback-textarea"
          />
        </el-form-item>
      </div>

      <!-- 联系方式 -->
      <div class="form-section">
        <h3 class="section-title">{{ t('feedback.contactInfoTitle') }}</h3>
        <el-form-item prop="contact_email">
          <el-input
            v-model="formData.contact_email"
            :placeholder="t('feedback.emailPlaceholder')"
            class="contact-input"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="contact_phone">
          <el-input
            v-model="formData.contact_phone"
            :placeholder="t('feedback.phonePlaceholder')"
            class="contact-input"
          >
            <template #prefix>
              <el-icon><Phone /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-button">{{ t('feedback.cancelButton') }}</el-button>
        <el-button
          type="primary"
          @click="submitFeedback"
          :loading="loading"
          class="submit-button"
          :disabled="buttonDisabled"
        >
          {{ t('feedback.submitButton') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped src="./Feedback.css"></style>
