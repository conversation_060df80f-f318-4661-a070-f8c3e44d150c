// BubbleList 组件的国际化内容
export const bubbleList = {
  zh: {
    // 通用按钮和操作
    copy: '复制',
    like: '点赞',
    unlike: '取消点赞',
    dislike: '踩',
    undislike: '取消踩',
    
    // 复制相关消息
    copySuccess: '已复制到剪贴板',
    copyFailed: '复制失败',
    
    // 反馈消息
    likeSuccess: '感谢您的反馈！',
    unlikeSuccess: '已取消点赞',
    undislikeSuccess: '已取消踩',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: '深度思考中...',
      completed: '已经思考完毕',
      content: '正在深度思考...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: '参考文献：',
      more: '更多',
      collapse: '收起'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: '本回答由 AI 生成，内容仅供参考，请仔细甄别',
      // VoiceView  
      voice: '本回答由 AI 生成，内容仅供参考，请仔细甄别',
      // KnowledgeView
      knowledge: '本文由 AI 生成，仅供医学参考，不能替代专业诊疗意见。请以临床医师的最终判断为准'
    }
  },
  
  en: {
    // 通用按钮和操作
    copy: 'Copy',
    like: 'Like',
    unlike: 'Unlike',
    dislike: 'Dislike',
    undislike: 'Undo Dislike',
    
    // 复制相关消息
    copySuccess: 'Copied to clipboard',
    copyFailed: 'Copy failed',
    
    // 反馈消息
    likeSuccess: 'Thank you for your feedback!',
    unlikeSuccess: 'Like canceled',
    undislikeSuccess: 'Dislike canceled',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: 'Deep thinking...',
      completed: 'Thinking completed',
      content: 'Deep thinking in progress...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: 'References:',
      more: 'More',
      collapse: 'Collapse'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: 'This response is generated by AI, for reference only, please verify carefully',
      // VoiceView  
      voice: 'This response is generated by AI, for reference only, please verify carefully',
      // KnowledgeView
      knowledge: 'This content is generated by AI for medical reference only and cannot replace professional medical advice. Please rely on the final judgment of clinical physicians'
    }
  },
  
  ja: {
    // 通用按钮和操作
    copy: 'コピー',
    like: 'いいね',
    unlike: 'いいねを取り消し',
    dislike: '悪い',
    undislike: '悪いを取り消し',
    
    // 复制相关消息
    copySuccess: 'クリップボードにコピーしました',
    copyFailed: 'コピーに失敗しました',
    
    // 反馈消息
    likeSuccess: 'フィードバックありがとうございます!',
    unlikeSuccess: 'いいねを取り消しました',
    undislikeSuccess: '悪いを取り消しました',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: '深く考え中...',
      completed: '思考完了',
      content: '深く考えています...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: '参考文献：',
      more: 'もっと',
      collapse: '閉じる'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: 'この回答はAIが生成したもので、参考用です。慎重にご確認ください',
      // VoiceView  
      voice: 'この回答はAIが生成したもので、参考用です。慎重にご確認ください',
      // KnowledgeView
      knowledge: 'この内容はAIが生成した医学参考用で、専門的な診療意見に代わるものではありません。臨床医師の最終的な判断を基準としてください'
    }
  },
  
  ko: {
    // 通用按钮和操作
    copy: '복사',
    like: '좋아요',
    unlike: '좋아요 취소',
    dislike: '싫어요',
    undislike: '싫어요 취소',
    
    // 复制相关消息
    copySuccess: '클립보드에 복사되었습니다',
    copyFailed: '복사에 실패했습니다',
    
    // 反馈消息
    likeSuccess: '피드백 감사합니다!',
    unlikeSuccess: '좋아요를 취소했습니다',
    undislikeSuccess: '싫어요를 취소했습니다',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: '깊이 생각 중...',
      completed: '사고 완료',
      content: '깊이 생각하고 있습니다...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: '참고문헌:',
      more: '더보기',
      collapse: '접기'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: '이 답변은 AI가 생성한 것으로 참고용입니다. 신중히 확인해 주세요',
      // VoiceView  
      voice: '이 답변은 AI가 생성한 것으로 참고용입니다. 신중히 확인해 주세요',
      // KnowledgeView
      knowledge: '이 내용은 AI가 생성한 의학 참고용으로 전문적인 진료 의견을 대체할 수 없습니다. 임상의사의 최종 판단을 기준으로 해주세요'
    }
  },
  
  th: {
    // 通用按钮和操作
    copy: 'คัดลอก',
    like: 'ถูกใจ',
    unlike: 'ยกเลิกถูกใจ',
    dislike: 'ไม่ถูกใจ',
    undislike: 'ยกเลิกไม่ถูกใจ',
    
    // 复制相关消息
    copySuccess: 'คัดลอกไปยังคลิปบอร์ดแล้ว',
    copyFailed: 'การคัดลอกล้มเหลว',
    
    // 反馈消息
    likeSuccess: 'ขอบคุณสำหรับข้อเสนอแนะของคุณ!',
    unlikeSuccess: 'ยกเลิกถูกใจแล้ว',
    undislikeSuccess: 'ยกเลิกไม่ถูกใจแล้ว',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: 'กำลังคิดอย่างลึกซึ้ง...',
      completed: 'คิดเสร็จแล้ว',
      content: 'กำลังคิดอย่างลึกซึ้ง...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: 'เอกสารอ้างอิง:',
      more: 'เพิ่มเติม',
      collapse: 'ยุบ'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: 'คำตอบนี้สร้างโดย AI สำหรับอ้างอิงเท่านั้น กรุณาตรวจสอบอย่างระมัดระวัง',
      // VoiceView  
      voice: 'คำตอบนี้สร้างโดย AI สำหรับอ้างอิงเท่านั้น กรุณาตรวจสอบอย่างระมัดระวัง',
      // KnowledgeView
      knowledge: 'เนื้อหานี้สร้างโดย AI สำหรับการอ้างอิงทางการแพทย์เท่านั้น ไม่สามารถทดแทนคำแนะนำการรักษาแบบมืออาชีพได้ กรุณาใช้การตัดสินใจสุดท้ายของแพทย์ผู้เชี่ยวชาญเป็นเกณฑ์'
    }
  },
  
  ms: {
    // 通用按钮和操作
    copy: 'Salin',
    like: 'Suka',
    unlike: 'Batal Suka',
    dislike: 'Tidak Suka',
    undislike: 'Batal Tidak Suka',
    
    // 复制相关消息
    copySuccess: 'Disalin ke papan klip',
    copyFailed: 'Gagal menyalin',
    
    // 反馈消息
    likeSuccess: 'Terima kasih atas maklum balas anda!',
    unlikeSuccess: 'Suka dibatalkan',
    undislikeSuccess: 'Tidak suka dibatalkan',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: 'Berfikir mendalam...',
      completed: 'Pemikiran selesai',
      content: 'Sedang berfikir mendalam...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: 'Rujukan:',
      more: 'Lagi',
      collapse: 'Runtuh'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: 'Jawapan ini dijana oleh AI, hanya untuk rujukan, sila sahkan dengan teliti',
      // VoiceView  
      voice: 'Jawapan ini dijana oleh AI, hanya untuk rujukan, sila sahkan dengan teliti',
      // KnowledgeView
      knowledge: 'Kandungan ini dijana oleh AI untuk rujukan perubatan sahaja dan tidak boleh menggantikan nasihat perubatan profesional. Sila bergantung pada keputusan akhir doktor klinikal'
    }
  },
  
  id: {
    // 通用按钮和操作
    copy: 'Salin',
    like: 'Suka',
    unlike: 'Batal Suka',
    dislike: 'Tidak Suka',
    undislike: 'Batal Tidak Suka',
    
    // 复制相关消息
    copySuccess: 'Disalin ke clipboard',
    copyFailed: 'Gagal menyalin',
    
    // 反馈消息
    likeSuccess: 'Terima kasih atas masukan Anda!',
    unlikeSuccess: 'Suka dibatalkan',
    undislikeSuccess: 'Tidak suka dibatalkan',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: 'Berpikir mendalam...',
      completed: 'Pemikiran selesai',
      content: 'Sedang berpikir mendalam...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: 'Referensi:',
      more: 'Lebih',
      collapse: 'Tutup'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: 'Jawaban ini dihasilkan oleh AI, hanya untuk referensi, mohon verifikasi dengan hati-hati',
      // VoiceView  
      voice: 'Jawaban ini dihasilkan oleh AI, hanya untuk referensi, mohon verifikasi dengan hati-hati',
      // KnowledgeView
      knowledge: 'Konten ini dihasilkan oleh AI untuk referensi medis saja dan tidak dapat menggantikan saran medis profesional. Mohon bergantung pada keputusan akhir dokter klinis'
    }
  },
  
  vi: {
    // 通用按钮和操作
    copy: 'Sao chép',
    like: 'Thích',
    unlike: 'Bỏ thích',
    dislike: 'Không thích',
    undislike: 'Bỏ không thích',
    
    // 复制相关消息
    copySuccess: 'Đã sao chép vào clipboard',
    copyFailed: 'Sao chép thất bại',
    
    // 反馈消息
    likeSuccess: 'Cảm ơn phản hồi của bạn!',
    unlikeSuccess: 'Đã bỏ thích',
    undislikeSuccess: 'Đã bỏ không thích',
    
    // 思考状态（ChatView和VoiceView）
    thinking: {
      loading: 'Đang suy nghĩ sâu...',
      completed: 'Hoàn thành suy nghĩ',
      content: 'Đang suy nghĩ sâu...'
    },
    
    // 参考文献（KnowledgeView特有）
    references: {
      title: 'Tài liệu tham khảo:',
      more: 'Thêm',
      collapse: 'Thu gọn'
    },
    
    // AI 生成内容声明
    disclaimer: {
      // ChatView
      chat: 'Câu trả lời này được tạo bởi AI, chỉ để tham khảo, vui lòng xác minh cẩn thận',
      // VoiceView  
      voice: 'Câu trả lời này được tạo bởi AI, chỉ để tham khảo, vui lòng xác minh cẩn thận',
      // KnowledgeView
      knowledge: 'Nội dung này được tạo bởi AI chỉ để tham khảo y tế và không thể thay thế lời khuyên y tế chuyên nghiệp. Vui lòng dựa vào quyết định cuối cùng của bác sĩ lâm sàng'
    }
  }
};