.app-header {
  height: var(--app-header-height);
  display: grid;
  grid-template-columns: 1fr 4fr 1fr;
  align-items: center;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.app-header > img {
  height: var(--app-header-height);
  cursor: pointer;
}

.nav-links {
  display: flex;
  gap: 2dvw;
  a {
    color: #374151;
    text-decoration: none;
    font-size: 0.9dvw;
    font-weight: bold;
    letter-spacing: 0.1dvw;
    padding: 0.4dvw 0.8dvw;
    border-radius: 0.4dvw;
    border: 0.15dvw solid transparent;
    transition:
      color 0.18s ease,
      background-color 0.18s ease;
  }
  a:hover {
    background-color: #f3f4f6;
    color: #1f2937;
    cursor: pointer;
  }
}

.app-header-user {
  text-align: right;
  padding-right: 1.2dvw;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1dvw;
}

.language-selector {
  display: flex;
  align-items: center;
}

.language-selector .el-select {
  width: 80px;
}

.language-selector .el-select .el-input__wrapper {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

.language-selector .el-select .el-input__wrapper:hover {
  border-color: #9ca3af;
}

/* 用户下拉菜单样式 */
.user-dropdown {
  cursor: pointer;
}

.user-avatar {
  width: 4.7dvh;
  height: 4.7dvh;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* 下拉菜单样式 */
.user-dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 6dvw;
}