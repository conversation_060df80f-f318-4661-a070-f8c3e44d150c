import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  AxiosError,
} from "axios";

import { useCommonStore } from "@/stores/common";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { TokenCookieManager } from "./cookieUtils";
import { getCurLanguage } from "./storageUtils";

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;
  showError?: boolean;
  showSuccess?: boolean;
}

// 错误处理类型
export interface ApiError {
  code: number;
  message: string;
  data?: unknown;
}

class ApiClient {
  private instance: AxiosInstance;
  private baseURL: string;
  private timeout: number;
  private loadingCount = 0;

  constructor(baseURL = "", timeout = 60000) {
    this.baseURL = baseURL;
    this.timeout = timeout;

    // 创建 axios 实例
    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // 设置请求拦截器
    this.setupRequestInterceptor();

    // 设置响应拦截器
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.instance.interceptors.request.use(
      async (config) => {
        // 显示加载状态
        if ((config as RequestConfig).showLoading !== false) {
          this.showLoading();
        }

        // 添加时间戳防止缓存
        if (config.method === "get") {
          config.params = {
            ...config.params,
            _t: Date.now(),
          };
        }
        config.headers.Authorization =
          "Bearer " + TokenCookieManager.getToken();
        config.headers.set("set-language", getCurLanguage() || "zh");
        return config;
      },
      (error) => {
        this.hideLoading();
        return Promise.reject(error);
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.instance.interceptors.response.use(
      (response) => {
        this.hideLoading();
        const cfg = (response.config || {}) as RequestConfig;
        // 获取国际化函数
        const { t } = useI18n();
        
        // 对于文件下载等二进制响应，直接返回原始响应，避免业务判断
        if (response.config && response.config.responseType === "blob") {
          // 可选：显示成功提示（一般下载不提示）
          if (cfg.showSuccess) {
            const msg = t('systemMessages.operationSuccess');
            // 延迟到下一个宏任务，避免阻塞主流程
            Promise.resolve().then(() => {
              ElMessage({ message: msg, type: "success" });
            });
          }
          return response; // 保持调用方拿到完整的 axios 响应
        }

        const { data, status } = response as any;
        const bizOk =
          (data &&
            (data.code === 200 ||
              data.success === true ||
              data.status === "success")) ||
          status === 200;
        if (bizOk) {
          if (cfg.showSuccess) {
            const msg = (data && (data.message || data.msg)) || t('systemMessages.operationSuccess');
            Promise.resolve().then(() => {
              ElMessage({ message: msg, type: "success" });
            });
          }
          // 优先返回 data.data，否则返回 data 本身
          return data && Object.prototype.hasOwnProperty.call(data, "data")
            ? data.data
            : data;
        } else {
          const apiError: ApiError = {
            code: (data && (data.code ?? status)) ?? -1,
            message: (data && (data.message || data.msg)) || t('systemMessages.requestFailed'),
            data: data && data.data,
          };
          if (cfg.showError !== false) {
            Promise.resolve().then(() => {
              ElMessage({ message: apiError.message, type: "error" });
            });
          }
          return Promise.reject(apiError);
        }
      },
      (error: AxiosError) => {
        const commonStore = useCommonStore();
        const { t } = useI18n();
        // 请求错误处理
        this.hideLoading();
        const cfg = ((error && error.config) || {}) as RequestConfig;

        let message = t('systemMessages.networkError');
        let codeNum = -1;
        const status =
          (error.response && (error.response.status as number)) || undefined;
        if (typeof status === "number") codeNum = status;

        if (codeNum === 401) {
          const token = TokenCookieManager.getToken()
          const text = token ? t('systemMessages.loginExpired') : t('systemMessages.notLoggedIn')
          ElMessage.warning(text);
          commonStore.isLogin = false;
          commonStore.showLogin = true;
          return Promise.reject(text);
        }

        if (
          error.code === "ECONNABORTED" ||
          (error.message || "").includes("timeout")
        ) {
          message = t('systemMessages.requestTimeout');
        }

        const respData: any = error.response?.data;
        if (respData) {
          // 某些后端返回 msg 字段
          message = respData.message || respData.msg || message;
        }

        if (cfg.showError !== false) {
          Promise.resolve().then(() => {
            ElMessage({ message, type: "error" });
          });
        }

        const apiError: ApiError = { code: codeNum, message, data: respData };
        return Promise.reject(apiError);
      }
    );
  }

  /**
   * 显示加载状态
   */
  private showLoading(): void {
    this.loadingCount++;
    // 这里可以集成你的全局 loading 组件
    // 例如：store.commit('setLoading', true)
  }

  /**
   * 隐藏加载状态
   */
  private hideLoading(): void {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      // 这里可以集成你的全局 loading 组件
      // 例如：store.commit('setLoading', false)
    }
  }

  /**
   * GET 请求
   */
  public get<T = unknown>(
    url: string,
    params?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.get(url, { ...config, params });
  }

  /**
   * POST 请求
   */
  public post<T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.post(url, data, config);
  }

  /**
   * PUT 请求
   */
  public put<T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.put(url, data, config);
  }

  /**
   * DELETE 请求
   */
  public delete<T = unknown>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.delete(url, config);
  }

  /**
   * PATCH 请求
   */
  public patch<T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.patch(url, data, config);
  }

  /**
   * 上传文件
   */
  public upload<T = unknown>(
    url: string,
    file: File,
    config?: RequestConfig
  ): Promise<T> {
    const formData = new FormData();
    formData.append("file", file);

    return this.instance.post(url, formData, {
      ...config,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /**
   * 下载文件
   */
  public download(
    url: string,
    filename?: string,
    config?: RequestConfig
  ): Promise<void> {
    return this.instance
      .get(url, {
        ...config,
        responseType: "blob",
      })
      .then((response) => {
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = filename || "download";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      });
  }

  /**
   * 设置默认请求头
   */
  public setHeader(key: string, value: string): void {
    this.instance.defaults.headers.common[key] = value;
  }

  /**
   * 移除请求头
   */
  public removeHeader(key: string): void {
    delete this.instance.defaults.headers.common[key];
  }
}

// 创建默认实例
const api = new ApiClient();

export default api;
export { ApiClient };

